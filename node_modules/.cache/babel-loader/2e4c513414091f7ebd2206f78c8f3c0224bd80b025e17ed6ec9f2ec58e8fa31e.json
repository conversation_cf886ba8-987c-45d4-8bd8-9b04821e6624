{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Projects/github.com/Izy-Mercado/izy-lp/src/LandingPageV200/LandingPageV200.jsx\";\nimport basket2 from \"./basket-2.svg\";\nimport chatgptImage10DeJulDe20251146051 from \"./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png\";\nimport apple from \"./apple.svg\";\nimport googlePlay from \"./google-play.svg\";\nimport \"./styleNew.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LandingPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"landing-page-new\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header-new\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"nav-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-new\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"logo-icon\",\n            alt: \"izy\",\n            src: basket2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"izy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"nav-link\",\n            children: \"Seja nosso parceiro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"nav-link\",\n            children: \"Suporte\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"cta-button\",\n          children: \"Quero fazer minhas compras com a Izy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: [\"Sua compra \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"highlight\",\n              children: \"ficou inteligente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 26\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle\",\n            children: \"Compare pre\\xE7os, economize tempo e receba em casa com a Izy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-image-container\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"hero-image\",\n            alt: \"Mulher usando smartphone para compras\",\n            src: chatgptImage10DeJulDe20251146051\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"app-showcase\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"showcase-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"showcase-title\",\n          children: \"Baixe o app e comece a economizar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"phones-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-mockup phone-1\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-screen\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"app-interface\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"app-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"app-title\",\n                    children: \"Lista de Compras\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 57,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"app-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Arroz 5kg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 61,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price\",\n                      children: \"R$ 18,90\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 62,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Feij\\xE3o 1kg\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 65,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price\",\n                      children: \"R$ 7,50\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 66,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 64,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"product-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\xD3leo de Soja\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 69,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"price\",\n                      children: \"R$ 4,20\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 70,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 68,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-mockup phone-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-screen\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"app-interface\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"app-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"app-title\",\n                    children: \"Comparar Pre\\xE7os\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"app-content\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"store-comparison\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"store-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"store-name\",\n                        children: \"Mercado A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 85,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"total-price\",\n                        children: \"R$ 30,60\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 86,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 84,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"store-item best-price\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"store-name\",\n                        children: \"Mercado B\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 89,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"total-price\",\n                        children: \"R$ 28,90\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 90,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 88,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"store-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"store-name\",\n                        children: \"Mercado C\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 93,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"total-price\",\n                        children: \"R$ 32,10\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 94,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 92,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-mockup phone-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-screen\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"app-interface\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"app-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"app-title\",\n                    children: \"Pedido Confirmado\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"app-content\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"order-status\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"status-icon\",\n                      children: \"\\u2713\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Seu pedido foi confirmado!\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"delivery-time\",\n                      children: \"Entrega em 2 horas\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"cashback-info\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Cashback: R$ 2,89\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 113,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 112,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"features-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"features-title\",\n          children: \"Como funciona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCDD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: \"Crie sua lista\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: \"Monte sua lista de compras de forma r\\xE1pida e intuitiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: \"Compare pre\\xE7os\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: \"Encontre os melhores pre\\xE7os nos mercados da sua regi\\xE3o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDE9A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: \"Receba em casa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: \"Suas compras chegam rapidinho na sua porta\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCB8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: \"Ganhe cashback\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: \"Receba dinheiro de volta a cada compra realizada\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"cta-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cta-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"cta-title\",\n          children: \"Pronto para economizar?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"cta-subtitle\",\n          children: \"Baixe o app da Izy e comece a fazer compras inteligentes hoje mesmo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"app-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"app-button\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: apple,\n              alt: \"Download na App Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-small\",\n                children: \"Baixe na\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-large\",\n                children: \"App Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"app-button\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: googlePlay,\n              alt: \"Dispon\\xEDvel no Google Play\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-small\",\n                children: \"Dispon\\xEDvel no\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-large\",\n                children: \"Google Play\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer-new\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"footer-logo-icon\",\n            alt: \"izy\",\n            src: basket2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"footer-logo-text\",\n            children: \"izy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-description\",\n          children: \"O futuro das compras \\xE9 local, digital e inteligente. Venha com a gente!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"footer-link\",\n            children: \"Seja um parceiro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"footer-link\",\n            children: \"Termos de uso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"footer-link\",\n            children: \"Privacidade\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"footer-link\",\n            children: \"Suporte\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = LandingPage;\nexport default LandingPageNew;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["basket2", "chatgptImage10DeJulDe20251146051", "apple", "googlePlay", "jsxDEV", "_jsxDEV", "LandingPage", "className", "children", "alt", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "LandingPageNew", "$RefreshReg$"], "sources": ["/home/<USER>/Projects/github.com/Izy-Mercado/izy-lp/src/LandingPageV200/LandingPageV200.jsx"], "sourcesContent": ["import basket2 from \"./basket-2.svg\";\nimport chatgptImage10DeJulDe20251146051 from \"./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png\";\nimport apple from \"./apple.svg\";\nimport googlePlay from \"./google-play.svg\";\nimport \"./styleNew.css\";\n\nexport const LandingPage = () => {\n  return (\n    <div className=\"landing-page-new\">\n      {/* Header */}\n      <header className=\"header-new\">\n        <nav className=\"nav-container\">\n          <div className=\"logo-new\">\n            <img className=\"logo-icon\" alt=\"izy\" src={basket2} />\n            <span className=\"logo-text\">izy</span>\n          </div>\n          <div className=\"nav-links\">\n            <a href=\"#\" className=\"nav-link\">Seja nosso parceiro</a>\n            <a href=\"#\" className=\"nav-link\">Suporte</a>\n          </div>\n          <button className=\"cta-button\">\n            Quero fazer minhas compras com a Izy\n          </button>\n        </nav>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"hero-section\">\n        <div className=\"hero-content\">\n          <div className=\"hero-text\">\n            <h1 className=\"hero-title\">\n              Sua compra <span className=\"highlight\">ficou inteligente</span>\n            </h1>\n            <p className=\"hero-subtitle\">\n              Compare preços, economize tempo e receba em casa com a Izy\n            </p>\n          </div>\n          <div className=\"hero-image-container\">\n            <img \n              className=\"hero-image\" \n              alt=\"Mulher usando smartphone para compras\" \n              src={chatgptImage10DeJulDe20251146051} \n            />\n          </div>\n        </div>\n      </section>\n\n      {/* Mobile App Showcase */}\n      <section className=\"app-showcase\">\n        <div className=\"showcase-content\">\n          <h2 className=\"showcase-title\">Baixe o app e comece a economizar</h2>\n          <div className=\"phones-container\">\n            <div className=\"phone-mockup phone-1\">\n              <div className=\"phone-screen\">\n                <div className=\"app-interface\">\n                  <div className=\"app-header\">\n                    <span className=\"app-title\">Lista de Compras</span>\n                  </div>\n                  <div className=\"app-content\">\n                    <div className=\"product-item\">\n                      <span>Arroz 5kg</span>\n                      <span className=\"price\">R$ 18,90</span>\n                    </div>\n                    <div className=\"product-item\">\n                      <span>Feijão 1kg</span>\n                      <span className=\"price\">R$ 7,50</span>\n                    </div>\n                    <div className=\"product-item\">\n                      <span>Óleo de Soja</span>\n                      <span className=\"price\">R$ 4,20</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"phone-mockup phone-2\">\n              <div className=\"phone-screen\">\n                <div className=\"app-interface\">\n                  <div className=\"app-header\">\n                    <span className=\"app-title\">Comparar Preços</span>\n                  </div>\n                  <div className=\"app-content\">\n                    <div className=\"store-comparison\">\n                      <div className=\"store-item\">\n                        <span className=\"store-name\">Mercado A</span>\n                        <span className=\"total-price\">R$ 30,60</span>\n                      </div>\n                      <div className=\"store-item best-price\">\n                        <span className=\"store-name\">Mercado B</span>\n                        <span className=\"total-price\">R$ 28,90</span>\n                      </div>\n                      <div className=\"store-item\">\n                        <span className=\"store-name\">Mercado C</span>\n                        <span className=\"total-price\">R$ 32,10</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"phone-mockup phone-3\">\n              <div className=\"phone-screen\">\n                <div className=\"app-interface\">\n                  <div className=\"app-header\">\n                    <span className=\"app-title\">Pedido Confirmado</span>\n                  </div>\n                  <div className=\"app-content\">\n                    <div className=\"order-status\">\n                      <div className=\"status-icon\">✓</div>\n                      <p>Seu pedido foi confirmado!</p>\n                      <p className=\"delivery-time\">Entrega em 2 horas</p>\n                      <div className=\"cashback-info\">\n                        <span>Cashback: R$ 2,89</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"features-section\">\n        <div className=\"features-content\">\n          <h2 className=\"features-title\">Como funciona</h2>\n          <div className=\"features-grid\">\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📝</div>\n              <h3 className=\"feature-title\">Crie sua lista</h3>\n              <p className=\"feature-description\">\n                Monte sua lista de compras de forma rápida e intuitiva\n              </p>\n            </div>\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">💰</div>\n              <h3 className=\"feature-title\">Compare preços</h3>\n              <p className=\"feature-description\">\n                Encontre os melhores preços nos mercados da sua região\n              </p>\n            </div>\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">🚚</div>\n              <h3 className=\"feature-title\">Receba em casa</h3>\n              <p className=\"feature-description\">\n                Suas compras chegam rapidinho na sua porta\n              </p>\n            </div>\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">💸</div>\n              <h3 className=\"feature-title\">Ganhe cashback</h3>\n              <p className=\"feature-description\">\n                Receba dinheiro de volta a cada compra realizada\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"cta-section\">\n        <div className=\"cta-content\">\n          <h2 className=\"cta-title\">Pronto para economizar?</h2>\n          <p className=\"cta-subtitle\">\n            Baixe o app da Izy e comece a fazer compras inteligentes hoje mesmo\n          </p>\n          <div className=\"app-buttons\">\n            <a href=\"#\" className=\"app-button\">\n              <img src={apple} alt=\"Download na App Store\" />\n              <div className=\"button-text\">\n                <span className=\"button-small\">Baixe na</span>\n                <span className=\"button-large\">App Store</span>\n              </div>\n            </a>\n            <a href=\"#\" className=\"app-button\">\n              <img src={googlePlay} alt=\"Disponível no Google Play\" />\n              <div className=\"button-text\">\n                <span className=\"button-small\">Disponível no</span>\n                <span className=\"button-large\">Google Play</span>\n              </div>\n            </a>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"footer-new\">\n        <div className=\"footer-content\">\n          <div className=\"footer-logo\">\n            <img className=\"footer-logo-icon\" alt=\"izy\" src={basket2} />\n            <span className=\"footer-logo-text\">izy</span>\n          </div>\n          <p className=\"footer-description\">\n            O futuro das compras é local, digital e inteligente. Venha com a gente!\n          </p>\n          <div className=\"footer-links\">\n            <a href=\"#\" className=\"footer-link\">Seja um parceiro</a>\n            <a href=\"#\" className=\"footer-link\">Termos de uso</a>\n            <a href=\"#\" className=\"footer-link\">Privacidade</a>\n            <a href=\"#\" className=\"footer-link\">Suporte</a>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default LandingPageNew;\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,OAAOC,gCAAgC,MAAM,kDAAkD;AAC/F,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC/B,oBACED,OAAA;IAAKE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/BH,OAAA;MAAQE,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAKE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBH,OAAA;YAAKE,SAAS,EAAC,WAAW;YAACE,GAAG,EAAC,KAAK;YAACC,GAAG,EAAEV;UAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDT,OAAA;YAAME,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACNT,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxDT,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNT,OAAA;UAAQE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAE/B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTT,OAAA;MAASE,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC/BH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAIE,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,aACd,eAAAH,OAAA;cAAME,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACLT,OAAA;YAAGE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNT,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCH,OAAA;YACEE,SAAS,EAAC,YAAY;YACtBE,GAAG,EAAC,uCAAuC;YAC3CC,GAAG,EAAET;UAAiC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAASE,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC/BH,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BH,OAAA;UAAIE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAiC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrET,OAAA;UAAKE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BH,OAAA;YAAKE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BH,OAAA;kBAAKE,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBH,OAAA;oBAAME,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACNT,OAAA;kBAAKE,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BH,OAAA;oBAAKE,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BH,OAAA;sBAAAG,QAAA,EAAM;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtBT,OAAA;sBAAME,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,eACNT,OAAA;oBAAKE,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BH,OAAA;sBAAAG,QAAA,EAAM;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvBT,OAAA;sBAAME,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,eACNT,OAAA;oBAAKE,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BH,OAAA;sBAAAG,QAAA,EAAM;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzBT,OAAA;sBAAME,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNT,OAAA;YAAKE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BH,OAAA;kBAAKE,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBH,OAAA;oBAAME,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNT,OAAA;kBAAKE,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1BH,OAAA;oBAAKE,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BH,OAAA;sBAAKE,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBH,OAAA;wBAAME,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CT,OAAA;wBAAME,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACNT,OAAA;sBAAKE,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,gBACpCH,OAAA;wBAAME,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CT,OAAA;wBAAME,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACNT,OAAA;sBAAKE,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBH,OAAA;wBAAME,SAAS,EAAC,YAAY;wBAAAC,QAAA,EAAC;sBAAS;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7CT,OAAA;wBAAME,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNT,OAAA;YAAKE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BH,OAAA;kBAAKE,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBH,OAAA;oBAAME,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACNT,OAAA;kBAAKE,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1BH,OAAA;oBAAKE,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BH,OAAA;sBAAKE,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACpCT,OAAA;sBAAAG,QAAA,EAAG;oBAA0B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACjCT,OAAA;sBAAGE,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACnDT,OAAA;sBAAKE,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5BH,OAAA;wBAAAG,QAAA,EAAM;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAASE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCH,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BH,OAAA;UAAIE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjDT,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BH,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCT,OAAA;cAAIE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDT,OAAA;cAAGE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNT,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCT,OAAA;cAAIE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDT,OAAA;cAAGE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNT,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCT,OAAA;cAAIE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDT,OAAA;cAAGE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNT,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCT,OAAA;cAAIE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDT,OAAA;cAAGE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAASE,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC9BH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAIE,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAuB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDT,OAAA;UAAGE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE5B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJT,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BH,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAChCH,OAAA;cAAKK,GAAG,EAAER,KAAM;cAACO,GAAG,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CT,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BH,OAAA;gBAAME,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CT,OAAA;gBAAME,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACJT,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAChCH,OAAA;cAAKK,GAAG,EAAEP,UAAW;cAACM,GAAG,EAAC;YAA2B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDT,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BH,OAAA;gBAAME,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDT,OAAA;gBAAME,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVT,OAAA;MAAQE,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BH,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BH,OAAA;YAAKE,SAAS,EAAC,kBAAkB;YAACE,GAAG,EAAC,KAAK;YAACC,GAAG,EAAEV;UAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DT,OAAA;YAAME,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNT,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJT,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxDT,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrDT,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnDT,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACE,EAAA,GAxMWV,WAAW;AA0MxB,eAAeW,cAAc;AAAC,IAAAD,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}