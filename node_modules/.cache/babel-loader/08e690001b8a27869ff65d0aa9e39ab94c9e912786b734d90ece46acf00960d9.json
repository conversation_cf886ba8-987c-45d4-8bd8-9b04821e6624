{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Projects/github.com/Izy-Mercado/izy-lp/src/LandingPageV200/LandingPageV200.jsx\";\nimport React from \"react\";\nimport basket2 from \"./basket-2.svg\";\nimport chatgptImage10DeJulDe20251146051 from \"./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png\";\nimport chatgptImage24DeMaiDe20251347411 from \"./chatgpt-image-24-de-mai-de-2025-13-47-41-1.png\";\nimport apple from \"./apple.svg\";\nimport googlePlay from \"./google-play.svg\";\nimport \"./styleNew.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LandingPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"landing-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"landing-page-v\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"div\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"div-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"div-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"shopping-ecommerce\",\n                alt: \"Shopping ecommerce\",\n                src: basket3\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 17,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-wrapper\",\n                children: \"izy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"p\",\n              children: \"O futuro das compras \\xE9 local, digital e inteligente. Venha com a gente!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img\",\n            alt: \"Social\",\n            src: social\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"div\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-right\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-2\",\n                  children: \"Seja um parceiro\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-2\",\n                  children: \"Termos de uso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"button\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-2\",\n                  children: \"Privacidade\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"store\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-status-pedido\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"img-2\",\n                alt: \"Social media apple\",\n                src: apple\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"div-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-3\",\n                  children: \"Dispon\\xEDvel na\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-4\",\n                  children: \"Apple Store\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-status-pedido\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"img-2\",\n                alt: \"Social media google\",\n                src: googlePlay\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"div-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-3\",\n                  children: \"Dispon\\xEDvel na\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-4\",\n                  children: \"Google Play\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-wrapper-5\",\n            children: \"Av. Engenheiro Roberto Freire, 1962, Swaway Shopping, Loja 13, Capim Macio, Natal/RN, 59082-095\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-5\",\n              children: \"\\xA9 2025, Izy Mercado. Todos os direitos reservado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pointer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-5\",\n              children: \"61.134.691/0001-00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-categorias\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"notification\",\n          alt: \"Notification\",\n          src: notification\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"div-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"img-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"group\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overlap-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"oval\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"path\",\n                  alt: \"Path\",\n                  src: path8\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"path-2\",\n                  alt: \"Path\",\n                  src: path2\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"path-3\",\n                  alt: \"Path\",\n                  src: path\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"path-4\",\n                  alt: \"Path\",\n                  src: path12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"div-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"img-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"group\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overlap-group-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"path-5\",\n                  alt: \"Path\",\n                  src: path6\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"path-6\",\n                  alt: \"Path\",\n                  src: path5\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"path-7\",\n                  alt: \"Path\",\n                  src: path17\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"path-8\",\n                  alt: \"Path\",\n                  src: path11\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"notification-2\",\n          alt: \"Notification\",\n          src: notification21\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"seu-mercado-f-cil-e\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"span\",\n          children: \"Seu mercado,\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-wrapper-6\",\n          children: \" f\\xE1cil e inteligente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"button-2\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-wrapper-7\",\n          children: \"Quero fazer minhas compras com a Izy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overlap\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"chatgpt-image-de\",\n          alt: \"Chatgpt image de\",\n          src: chatgptImage10DeJulDe20251146051\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-4\",\n            alt: \"Shopping ecommerce\",\n            src: basket\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-8\",\n              children: \"Sua lista de compras em minutos!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-9\",\n              children: \"Em at\\xE9 5min, voc\\xEA cria sua lista, compara os pre\\xE7os e recebe em casa. Tudo num s\\xF3 app\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-4\",\n            alt: \"Dollar front color\",\n            src: dollarFrontColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-8\",\n              children: \"Receba dinheiro de volta\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-9\",\n              children: \"Na Izy, voc\\xEA ganha comodidade e ainda ganha cashback\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"button-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-wrapper-10\",\n            children: \"Compras do m\\xEAs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-2\",\n            alt: \"Interface essential\",\n            src: starsLightSparkle14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"title-wrapper\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"img-3\",\n                alt: \"Interface essential\",\n                src: starsLightSparkle12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-wrapper-11\",\n                children: \"Crie sua lista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overlap-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"img-3\",\n                alt: \"Shopping ecommerce\",\n                src: basket4\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-wrapper-11\",\n                children: \"Cote os melhores pre\\xE7os\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overlap-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"img-3\",\n                alt: \"Dollar front color\",\n                src: dollarFrontColor\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"img\",\n                alt: \"Economize e ganhe\",\n                src: economizeEGanheCashback\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"store-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-2\",\n            alt: \"Social media apple\",\n            src: apple\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"div-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-3\",\n              children: \"Dispon\\xEDvel na\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-4\",\n              children: \"Apple Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-2\",\n            alt: \"Social media google\",\n            src: googlePlay\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"div-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-3\",\n              children: \"Dispon\\xEDvel na\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-4\",\n              children: \"Google Play\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"list-types\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"img-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overlap-group-wrapper\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overlap-group-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-9\",\n                    alt: \"Path\",\n                    src: path4\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-10\",\n                    alt: \"Path\",\n                    src: path9\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-11\",\n                    alt: \"Path\",\n                    src: path13\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-12\",\n                    alt: \"Path\",\n                    src: path3\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-13\",\n                    alt: \"Path\",\n                    src: path15\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-14\",\n                    alt: \"Path\",\n                    src: path16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-15\",\n                    alt: \"Path\",\n                    src: path16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-16\",\n                    alt: \"Path\",\n                    src: path16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"seu-mercado-para\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"span\",\n            children: \"Seu mercado,\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-wrapper-6\",\n            children: \" para todos os momentos!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"img-4\",\n          alt: \"Shopping ecommerce\",\n          src: basket31\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-wrapper-12\",\n          children: \"Receba seus produtos fresquinhos, baixe nosso app!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overlap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"chatgpt-image-de-2\",\n          alt: \"Chatgpt image de\",\n          src: chatgptImage10DeJulDe202511460512\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-4\",\n            alt: \"Shopping ecommerce\",\n            src: basket21\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-8\",\n              children: \"Mais visibilidade para o seu neg\\xF3cio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-9\",\n              children: \"Seja parceiro da Izy e comece a vender mais, com nossa tecnologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-4\",\n            alt: \"Interface essential\",\n            src: starsLightSparkle13\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-8\",\n              children: \"Relat\\xF3rios e dados de performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-9\",\n              children: \"Com a Izy, voc\\xEA sabe exatamente o que vender e por quanto vai vender\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"button-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"div\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"div-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"product-2\",\n                    alt: \"Product\",\n                    src: product\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-13\",\n                  children: \"Supermercado Konoha\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tag\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"img-5\",\n                  alt: \"Interface essential\",\n                  src: starsLightSparkle1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-14\",\n                  children: \"Mais completo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"div\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"content-categorias-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-wrapper\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"product-3\",\n                    alt: \"Product\",\n                    src: product1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"imagem-wrapper\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"imagem\",\n                    alt: \"Imagem\",\n                    src: imagem1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"notification-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-wrapper-15\",\n                    children: \"28\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"div-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-wrapper-16\",\n                  children: \"Total do pedido\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"div-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-wrapper-17\",\n                    children: \"R$ 102,00\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"img-6\",\n                    alt: \"Arrows diagrams\",\n                    src: arrow\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overlap-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"chatgpt-image-de-3\",\n          alt: \"Chatgpt image de\",\n          src: chatgptImage10DeJulDe20251146052\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-3\",\n            alt: \"Chatgpt image de\",\n            src: chatgptImage24DeMaiDe20251347411\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-18\",\n              children: \"Voc\\xEA ganhou R$ 12,00 de cashback!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"voc-vai-receber-um\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-wrapper-19\",\n                children: \"Voc\\xEA vai receber um PIX na conta onde voc\\xEA pagou o seu pedido\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-7\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-documento\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"img-wrapper\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                className: \"product-4\",\n                alt: \"Product\",\n                src: product2\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-wrapper-20\",\n                children: \"Mercado\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-wrapper-13\",\n                children: \"Super Econ\\xF4mico\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"frame\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"frame\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"frame\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"frame\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hora\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-21\",\n              children: \"15:25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"frame-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-22\",\n              children: \"Conclu\\xEDdo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-7\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-wrapper-23\",\n          children: \"Aumente suas vendas e conquiste novos clientes com a Izy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"button-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-wrapper-7\",\n            children: \"Quero vender pela Izy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"notification-5\",\n            alt: \"Notification\",\n            src: notification3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"div-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"img-3\",\n              alt: \"Shopping ecommerce\",\n              src: basket1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-24\",\n              children: \"Izy Mercado\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"notification-5\",\n            alt: \"Notification\",\n            src: notification4\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"div-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"img-3\",\n              alt: \"Shopping ecommerce\",\n              src: basket1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-24\",\n              children: \"Izy Hortifruti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"notification-5\",\n            alt: \"Notification\",\n            src: notification2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"div-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"img-3\",\n              alt: \"Shopping ecommerce\",\n              src: basket1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-24\",\n              children: \"Izy Padaria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"notification-5\",\n            alt: \"Notification\",\n            src: notification1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"div-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"img-3\",\n              alt: \"Shopping ecommerce\",\n              src: basket1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-24\",\n              children: \"Izy Carnes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"notification-5\",\n            alt: \"Notification\",\n            src: notification5\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"div-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"img-3\",\n              alt: \"Shopping ecommerce\",\n              src: basket1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-wrapper-24\",\n              children: \"Izy Fit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-9\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"img-4\",\n          alt: \"Interface essential\",\n          src: heartFavorite\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-wrapper-25\",\n          children: \"O que falam da Izy?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"button-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-wrapper-7\",\n          children: \"Quero fazer minhas compras com a Izy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"depoimento\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-status-pedido-9\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"notification-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"img-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overlap-group-wrapper\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overlap-group-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"rectangle\",\n                    alt: \"Rectangle\",\n                    src: rectangle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-17\",\n                    alt: \"Path\",\n                    src: path7\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-18\",\n                    alt: \"Path\",\n                    src: image1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-19\",\n                    alt: \"Path\",\n                    src: path9\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"rectangle-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-20\",\n                    alt: \"Path\",\n                    src: path14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-21\",\n                    alt: \"Path\",\n                    src: path10\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: \"path-22\",\n                    alt: \"Path\",\n                    src: path9\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-wrapper-26\",\n              children: \"Com a Izy, n\\xE3o preciso mais ficar horas num supermercado esperando numa fila, recebo meus produtos no conforto de minha casa!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"content-11\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-wrapper-27\",\n                children: \"L\\xEDvia Maria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-wrapper-9\",\n                children: \"Cliente Izy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"steps-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"frame-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"frame-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"frame-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img\",\n            alt: \"Actions\",\n            src: actions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-status-pedido-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"img\",\n          alt: \"Quer GANHAR r\",\n          src: QUERGanharR50\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"use-o-cupom\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-wrapper-28\",\n            children: \"Use o cupom \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-wrapper-29\",\n            children: \"50IZY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-wrapper-30\",\n          children: \"*Para compras acima de R$ 500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 612,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"img-2\",\n            alt: \"Shopping ecommerce\",\n            src: basket2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-wrapper-31\",\n            children: \"izy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-wrapper-32\",\n            children: \"Seja nosso parceiro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-wrapper-32\",\n            children: \"Suporte\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"button-7\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-wrapper-7\",\n            children: \"Quero fazer minhas compras com a Izy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "basket2", "chatgptImage10DeJulDe20251146051", "chatgptImage24DeMaiDe20251347411", "apple", "googlePlay", "jsxDEV", "_jsxDEV", "LandingPage", "className", "children", "alt", "src", "basket3", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "social", "notification", "path8", "path2", "path", "path12", "path6", "path5", "path17", "path11", "notification21", "basket", "dollarFrontColor", "starsLightSparkle14", "starsLightSparkle12", "basket4", "economizeEGanheCashback", "path4", "path9", "path13", "path3", "path15", "path16", "basket31", "chatgptImage10DeJulDe202511460512", "basket21", "starsLightSparkle13", "product", "starsLightSparkle1", "product1", "imagem1", "arrow", "chatgptImage10DeJulDe20251146052", "product2", "notification3", "basket1", "notification4", "notification2", "notification1", "notification5", "heartFavorite", "rectangle", "path7", "image1", "path14", "path10", "actions", "QUERGanharR50", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Projects/github.com/Izy-Mercado/izy-lp/src/LandingPageV200/LandingPageV200.jsx"], "sourcesContent": ["import React from \"react\";\nimport basket2 from \"./basket-2.svg\";\nimport chatgptImage10DeJulDe20251146051 from \"./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png\";\nimport chatgptImage24DeMaiDe20251347411 from \"./chatgpt-image-24-de-mai-de-2025-13-47-41-1.png\";\nimport apple from \"./apple.svg\";\nimport googlePlay from \"./google-play.svg\";\nimport \"./styleNew.css\";\n\nexport const LandingPage = () => {\n  return (\n    <div className=\"landing-page\">\n      <div className=\"landing-page-v\">\n        <div className=\"footer\">\n          <div className=\"div\">\n            <div className=\"div-2\">\n              <div className=\"div-3\">\n                <img\n                  className=\"shopping-ecommerce\"\n                  alt=\"Shopping ecommerce\"\n                  src={basket3}\n                />\n\n                <div className=\"text-wrapper\">izy</div>\n              </div>\n\n              <p className=\"p\">\n                O futuro das compras é local, digital e inteligente. Venha com a\n                gente!\n              </p>\n            </div>\n\n            <img className=\"img\" alt=\"Social\" src={social} />\n          </div>\n\n          <div className=\"div\">\n            <div className=\"content\">\n              <div className=\"content-right\">\n                <button className=\"button\">\n                  <div className=\"text-wrapper-2\">Seja um parceiro</div>\n                </button>\n\n                <button className=\"button\">\n                  <div className=\"text-wrapper-2\">Termos de uso</div>\n                </button>\n\n                <button className=\"button\">\n                  <div className=\"text-wrapper-2\">Privacidade</div>\n                </button>\n              </div>\n            </div>\n\n            <div className=\"store\">\n              <div className=\"card-status-pedido\">\n                <img className=\"img-2\" alt=\"Social media apple\" src={apple} />\n\n                <div className=\"div-4\">\n                  <div className=\"text-wrapper-3\">Disponível na</div>\n\n                  <div className=\"text-wrapper-4\">Apple Store</div>\n                </div>\n              </div>\n\n              <div className=\"card-status-pedido\">\n                <img\n                  className=\"img-2\"\n                  alt=\"Social media google\"\n                  src={googlePlay}\n                />\n\n                <div className=\"div-4\">\n                  <div className=\"text-wrapper-3\">Disponível na</div>\n\n                  <div className=\"text-wrapper-4\">Google Play</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"text-content\">\n            <p className=\"text-wrapper-5\">\n              Av. Engenheiro Roberto Freire, 1962, Swaway Shopping, Loja 13,\n              Capim Macio, Natal/RN, 59082-095\n            </p>\n\n            <div className=\"company\">\n              <p className=\"text-wrapper-5\">\n                © 2025, Izy Mercado. Todos os direitos reservado\n              </p>\n\n              <div className=\"pointer\" />\n\n              <div className=\"text-wrapper-5\">61.134.691/0001-00</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"content-categorias\">\n          <img className=\"notification\" alt=\"Notification\" src={notification} />\n\n          <div className=\"div-wrapper\">\n            <div className=\"img-3\">\n              <div className=\"group\">\n                <div className=\"overlap-group\">\n                  <div className=\"oval\" />\n\n                  <img className=\"path\" alt=\"Path\" src={path8} />\n\n                  <img className=\"path-2\" alt=\"Path\" src={path2} />\n\n                  <img className=\"path-3\" alt=\"Path\" src={path} />\n\n                  <img className=\"path-4\" alt=\"Path\" src={path12} />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"div-wrapper\">\n            <div className=\"img-3\">\n              <div className=\"group\">\n                <div className=\"overlap-group-2\">\n                  <img className=\"path-5\" alt=\"Path\" src={path6} />\n\n                  <img className=\"path-6\" alt=\"Path\" src={path5} />\n\n                  <img className=\"path-7\" alt=\"Path\" src={path17} />\n\n                  <img className=\"path-8\" alt=\"Path\" src={path11} />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <img\n            className=\"notification-2\"\n            alt=\"Notification\"\n            src={notification21}\n          />\n        </div>\n\n        <p className=\"seu-mercado-f-cil-e\">\n          <span className=\"span\">Seu mercado,</span>\n\n          <span className=\"text-wrapper-6\"> fácil e inteligente</span>\n        </p>\n\n        <div className=\"button-2\">\n          <p className=\"text-wrapper-7\">Quero fazer minhas compras com a Izy</p>\n        </div>\n\n        <div className=\"overlap\">\n          <img\n            className=\"chatgpt-image-de\"\n            alt=\"Chatgpt image de\"\n            src={chatgptImage10DeJulDe20251146051}\n          />\n\n          <div className=\"card-status-pedido-2\">\n            <img className=\"img-4\" alt=\"Shopping ecommerce\" src={basket} />\n\n            <div className=\"content-2\">\n              <p className=\"text-wrapper-8\">Sua lista de compras em minutos!</p>\n\n              <p className=\"text-wrapper-9\">\n                Em até 5min, você cria sua lista, compara os preços e recebe em\n                casa. Tudo num só app\n              </p>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido-3\">\n            <img\n              className=\"img-4\"\n              alt=\"Dollar front color\"\n              src={dollarFrontColor}\n            />\n\n            <div className=\"content-2\">\n              <div className=\"text-wrapper-8\">Receba dinheiro de volta</div>\n\n              <p className=\"text-wrapper-9\">\n                Na Izy, você ganha comodidade e ainda ganha cashback\n              </p>\n            </div>\n          </div>\n\n          <button className=\"button-3\">\n            <div className=\"text-wrapper-10\">Compras do mês</div>\n\n            <img\n              className=\"img-2\"\n              alt=\"Interface essential\"\n              src={starsLightSparkle14}\n            />\n          </button>\n        </div>\n\n        <div className=\"content-3\">\n          <div className=\"card\">\n            <div className=\"title-wrapper\">\n              <div className=\"title\">\n                <img\n                  className=\"img-3\"\n                  alt=\"Interface essential\"\n                  src={starsLightSparkle12}\n                />\n\n                <div className=\"text-wrapper-11\">Crie sua lista</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"overlap-2\">\n              <div className=\"title-2\">\n                <img className=\"img-3\" alt=\"Shopping ecommerce\" src={basket4} />\n\n                <div className=\"text-wrapper-11\">Cote os melhores preços</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"overlap-3\">\n              <div className=\"title-3\">\n                <img\n                  className=\"img-3\"\n                  alt=\"Dollar front color\"\n                  src={dollarFrontColor}\n                />\n\n                <img\n                  className=\"img\"\n                  alt=\"Economize e ganhe\"\n                  src={economizeEGanheCashback}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"store-2\">\n          <div className=\"card-status-pedido\">\n            <img className=\"img-2\" alt=\"Social media apple\" src={apple} />\n\n            <div className=\"div-4\">\n              <div className=\"text-wrapper-3\">Disponível na</div>\n\n              <div className=\"text-wrapper-4\">Apple Store</div>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido\">\n            <img className=\"img-2\" alt=\"Social media google\" src={googlePlay} />\n\n            <div className=\"div-4\">\n              <div className=\"text-wrapper-3\">Disponível na</div>\n\n              <div className=\"text-wrapper-4\">Google Play</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"content-4\">\n          <div className=\"list-types\">\n            <div className=\"notification-3\">\n              <div className=\"img-2\">\n                <div className=\"overlap-group-wrapper\">\n                  <div className=\"overlap-group-3\">\n                    <img className=\"path-9\" alt=\"Path\" src={path4} />\n\n                    <img className=\"path-10\" alt=\"Path\" src={path9} />\n\n                    <img className=\"path-11\" alt=\"Path\" src={path13} />\n\n                    <img className=\"path-12\" alt=\"Path\" src={path3} />\n\n                    <img className=\"path-13\" alt=\"Path\" src={path15} />\n\n                    <img className=\"path-14\" alt=\"Path\" src={path16} />\n\n                    <img className=\"path-15\" alt=\"Path\" src={path16} />\n\n                    <img className=\"path-16\" alt=\"Path\" src={path16} />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <p className=\"seu-mercado-para\">\n            <span className=\"span\">Seu mercado,</span>\n\n            <span className=\"text-wrapper-6\"> para todos os momentos!</span>\n          </p>\n        </div>\n\n        <div className=\"content-5\">\n          <img className=\"img-4\" alt=\"Shopping ecommerce\" src={basket31} />\n\n          <p className=\"text-wrapper-12\">\n            Receba seus produtos fresquinhos, baixe nosso app!\n          </p>\n        </div>\n\n        <div className=\"overlap-4\">\n          <img\n            className=\"chatgpt-image-de-2\"\n            alt=\"Chatgpt image de\"\n            src={chatgptImage10DeJulDe202511460512}\n          />\n\n          <div className=\"card-status-pedido-4\">\n            <img className=\"img-4\" alt=\"Shopping ecommerce\" src={basket21} />\n\n            <div className=\"content-2\">\n              <p className=\"text-wrapper-8\">\n                Mais visibilidade para o seu negócio\n              </p>\n\n              <p className=\"text-wrapper-9\">\n                Seja parceiro da Izy e comece a vender mais, com nossa\n                tecnologia\n              </p>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido-5\">\n            <img\n              className=\"img-4\"\n              alt=\"Interface essential\"\n              src={starsLightSparkle13}\n            />\n\n            <div className=\"content-2\">\n              <p className=\"text-wrapper-8\">\n                Relatórios e dados de performance\n              </p>\n\n              <p className=\"text-wrapper-9\">\n                Com a Izy, você sabe exatamente o que vender e por quanto vai\n                vender\n              </p>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido-6\">\n            <div className=\"button-4\">\n              <div className=\"div\">\n                <div className=\"div-2\">\n                  <div className=\"product\">\n                    <img className=\"product-2\" alt=\"Product\" src={product} />\n                  </div>\n\n                  <div className=\"text-wrapper-13\">Supermercado Konoha</div>\n                </div>\n\n                <div className=\"tag\">\n                  <img\n                    className=\"img-5\"\n                    alt=\"Interface essential\"\n                    src={starsLightSparkle1}\n                  />\n\n                  <div className=\"text-wrapper-14\">Mais completo</div>\n                </div>\n              </div>\n\n              <div className=\"div\">\n                <div className=\"content-categorias-2\">\n                  <div className=\"product-wrapper\">\n                    <img className=\"product-3\" alt=\"Product\" src={product1} />\n                  </div>\n\n                  <div className=\"imagem-wrapper\">\n                    <img className=\"imagem\" alt=\"Imagem\" src={imagem1} />\n                  </div>\n\n                  <div className=\"notification-4\">\n                    <div className=\"text-wrapper-15\">28</div>\n                  </div>\n                </div>\n\n                <div className=\"div-4\">\n                  <div className=\"text-wrapper-16\">Total do pedido</div>\n\n                  <div className=\"div-3\">\n                    <div className=\"text-wrapper-17\">R$ 102,00</div>\n\n                    <img className=\"img-6\" alt=\"Arrows diagrams\" src={arrow} />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"overlap-5\">\n          <img\n            className=\"chatgpt-image-de-3\"\n            alt=\"Chatgpt image de\"\n            src={chatgptImage10DeJulDe20251146052}\n          />\n\n          <div className=\"content-text\">\n            <img\n              className=\"img-3\"\n              alt=\"Chatgpt image de\"\n              src={chatgptImage24DeMaiDe20251347411}\n            />\n\n            <div className=\"content-2\">\n              <p className=\"text-wrapper-18\">\n                Você ganhou R$ 12,00 de cashback!\n              </p>\n\n              <p className=\"voc-vai-receber-um\">\n                <span className=\"text-wrapper-19\">\n                  Você vai receber um PIX na conta onde você pagou o seu pedido\n                </span>\n              </p>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido-7\">\n            <div className=\"card-documento\">\n              <div className=\"img-wrapper\">\n                <img className=\"product-4\" alt=\"Product\" src={product2} />\n              </div>\n\n              <div className=\"content-6\">\n                <div className=\"text-wrapper-20\">Mercado</div>\n\n                <div className=\"text-wrapper-13\">Super Econômico</div>\n              </div>\n            </div>\n\n            <div className=\"steps\">\n              <div className=\"frame\" />\n\n              <div className=\"frame\" />\n\n              <div className=\"frame\" />\n\n              <div className=\"frame\" />\n            </div>\n\n            <div className=\"hora\">\n              <div className=\"text-wrapper-21\">15:25</div>\n\n              <div className=\"frame-2\" />\n\n              <div className=\"text-wrapper-22\">Concluído</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"content-7\">\n          <p className=\"text-wrapper-23\">\n            Aumente suas vendas e conquiste novos clientes com a Izy\n          </p>\n\n          <button className=\"button-5\">\n            <div className=\"text-wrapper-7\">Quero vender pela Izy</div>\n          </button>\n        </div>\n\n        <div className=\"content-8\">\n          <div className=\"card-status-pedido-8\">\n            <img\n              className=\"notification-5\"\n              alt=\"Notification\"\n              src={notification3}\n            />\n\n            <div className=\"div-3\">\n              <img className=\"img-3\" alt=\"Shopping ecommerce\" src={basket1} />\n\n              <div className=\"text-wrapper-24\">Izy Mercado</div>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido-8\">\n            <img\n              className=\"notification-5\"\n              alt=\"Notification\"\n              src={notification4}\n            />\n\n            <div className=\"div-3\">\n              <img className=\"img-3\" alt=\"Shopping ecommerce\" src={basket1} />\n\n              <div className=\"text-wrapper-24\">Izy Hortifruti</div>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido-8\">\n            <img\n              className=\"notification-5\"\n              alt=\"Notification\"\n              src={notification2}\n            />\n\n            <div className=\"div-3\">\n              <img className=\"img-3\" alt=\"Shopping ecommerce\" src={basket1} />\n\n              <div className=\"text-wrapper-24\">Izy Padaria</div>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido-8\">\n            <img\n              className=\"notification-5\"\n              alt=\"Notification\"\n              src={notification1}\n            />\n\n            <div className=\"div-3\">\n              <img className=\"img-3\" alt=\"Shopping ecommerce\" src={basket1} />\n\n              <div className=\"text-wrapper-24\">Izy Carnes</div>\n            </div>\n          </div>\n\n          <div className=\"card-status-pedido-8\">\n            <img\n              className=\"notification-5\"\n              alt=\"Notification\"\n              src={notification5}\n            />\n\n            <div className=\"div-3\">\n              <img className=\"img-3\" alt=\"Shopping ecommerce\" src={basket1} />\n\n              <div className=\"text-wrapper-24\">Izy Fit</div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"content-9\">\n          <img\n            className=\"img-4\"\n            alt=\"Interface essential\"\n            src={heartFavorite}\n          />\n\n          <p className=\"text-wrapper-25\">O que falam da Izy?</p>\n        </div>\n\n        <div className=\"button-6\">\n          <p className=\"text-wrapper-7\">Quero fazer minhas compras com a Izy</p>\n        </div>\n\n        <div className=\"depoimento\">\n          <div className=\"card-status-pedido-9\">\n            <div className=\"notification-3\">\n              <div className=\"img-2\">\n                <div className=\"overlap-group-wrapper\">\n                  <div className=\"overlap-group-4\">\n                    <img\n                      className=\"rectangle\"\n                      alt=\"Rectangle\"\n                      src={rectangle}\n                    />\n\n                    <img className=\"path-17\" alt=\"Path\" src={path7} />\n\n                    <img className=\"path-18\" alt=\"Path\" src={image1} />\n\n                    <img className=\"path-19\" alt=\"Path\" src={path9} />\n\n                    <div className=\"rectangle-2\" />\n\n                    <img className=\"path-20\" alt=\"Path\" src={path14} />\n\n                    <img className=\"path-21\" alt=\"Path\" src={path10} />\n\n                    <img className=\"path-22\" alt=\"Path\" src={path9} />\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"content-10\">\n              <p className=\"text-wrapper-26\">\n                Com a Izy, não preciso mais ficar horas num supermercado\n                esperando numa fila, recebo meus produtos no conforto de minha\n                casa!\n              </p>\n\n              <div className=\"content-11\">\n                <div className=\"text-wrapper-27\">Lívia Maria</div>\n\n                <div className=\"text-wrapper-9\">Cliente Izy</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"footer-2\">\n            <div className=\"steps-2\">\n              <div className=\"frame-3\" />\n\n              <div className=\"frame-4\" />\n\n              <div className=\"frame-4\" />\n            </div>\n\n            <img className=\"img\" alt=\"Actions\" src={actions} />\n          </div>\n        </div>\n\n        <div className=\"card-status-pedido-10\">\n          <img className=\"img\" alt=\"Quer GANHAR r\" src={QUERGanharR50} />\n\n          <p className=\"use-o-cupom\">\n            <span className=\"text-wrapper-28\">Use o cupom </span>\n\n            <span className=\"text-wrapper-29\">50IZY</span>\n          </p>\n\n          <p className=\"text-wrapper-30\">*Para compras acima de R$ 500</p>\n        </div>\n\n        <div className=\"menu\">\n          <div className=\"logo\">\n            <img className=\"img-2\" alt=\"Shopping ecommerce\" src={basket2} />\n\n            <div className=\"text-wrapper-31\">izy</div>\n          </div>\n\n          <div className=\"menu-2\">\n            <div className=\"text-wrapper-32\">Seja nosso parceiro</div>\n\n            <div className=\"text-wrapper-32\">Suporte</div>\n          </div>\n\n          <div className=\"button-7\">\n            <p className=\"text-wrapper-7\">\n              Quero fazer minhas compras com a Izy\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,gCAAgC,MAAM,kDAAkD;AAC/F,OAAOC,gCAAgC,MAAM,kDAAkD;AAC/F,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC/B,oBACED,OAAA;IAAKE,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3BH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BH,OAAA;QAAKE,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBH,OAAA;UAAKE,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACpBH,OAAA;gBACEE,SAAS,EAAC,oBAAoB;gBAC9BE,GAAG,EAAC,oBAAoB;gBACxBC,GAAG,EAAEC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAEFV,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eAENV,OAAA;cAAGE,SAAS,EAAC,GAAG;cAAAC,QAAA,EAAC;YAGjB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENV,OAAA;YAAKE,SAAS,EAAC,KAAK;YAACE,GAAG,EAAC,QAAQ;YAACC,GAAG,EAAEM;UAAO;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBH,OAAA;YAAKE,SAAS,EAAC,SAAS;YAAAC,QAAA,eACtBH,OAAA;cAAKE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BH,OAAA;gBAAQE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eACxBH,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eAETV,OAAA;gBAAQE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eACxBH,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eAETV,OAAA;gBAAQE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,eACxBH,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCH,OAAA;gBAAKE,SAAS,EAAC,OAAO;gBAACE,GAAG,EAAC,oBAAoB;gBAACC,GAAG,EAAER;cAAM;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE9DV,OAAA;gBAAKE,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBACpBH,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAEnDV,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENV,OAAA;cAAKE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCH,OAAA;gBACEE,SAAS,EAAC,OAAO;gBACjBE,GAAG,EAAC,qBAAqB;gBACzBC,GAAG,EAAEP;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eAEFV,OAAA;gBAAKE,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBACpBH,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAEnDV,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YAAGE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAG9B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJV,OAAA;YAAKE,SAAS,EAAC,SAAS;YAAAC,QAAA,gBACtBH,OAAA;cAAGE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAE9B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJV,OAAA;cAAKE,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE3BV,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCH,OAAA;UAAKE,SAAS,EAAC,cAAc;UAACE,GAAG,EAAC,cAAc;UAACC,GAAG,EAAEO;QAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEtEV,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAAAC,QAAA,eACpBH,OAAA;gBAAKE,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BH,OAAA;kBAAKE,SAAS,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAExBV,OAAA;kBAAKE,SAAS,EAAC,MAAM;kBAACE,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAEQ;gBAAM;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAE/CV,OAAA;kBAAKE,SAAS,EAAC,QAAQ;kBAACE,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAES;gBAAM;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEjDV,OAAA;kBAAKE,SAAS,EAAC,QAAQ;kBAACE,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAEU;gBAAK;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEhDV,OAAA;kBAAKE,SAAS,EAAC,QAAQ;kBAACE,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAEW;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,eACpBH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAAAC,QAAA,eACpBH,OAAA;gBAAKE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BH,OAAA;kBAAKE,SAAS,EAAC,QAAQ;kBAACE,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAEY;gBAAM;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEjDV,OAAA;kBAAKE,SAAS,EAAC,QAAQ;kBAACE,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAEa;gBAAM;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEjDV,OAAA;kBAAKE,SAAS,EAAC,QAAQ;kBAACE,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAEc;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAElDV,OAAA;kBAAKE,SAAS,EAAC,QAAQ;kBAACE,GAAG,EAAC,MAAM;kBAACC,GAAG,EAAEe;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UACEE,SAAS,EAAC,gBAAgB;UAC1BE,GAAG,EAAC,cAAc;UAClBC,GAAG,EAAEgB;QAAe;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENV,OAAA;QAAGE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCH,OAAA;UAAME,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAE1CV,OAAA;UAAME,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eAEJV,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBH,OAAA;UAAGE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtBH,OAAA;UACEE,SAAS,EAAC,kBAAkB;UAC5BE,GAAG,EAAC,kBAAkB;UACtBC,GAAG,EAAEV;QAAiC;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eAEFV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAACE,GAAG,EAAC,oBAAoB;YAACC,GAAG,EAAEiB;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE/DV,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAGE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAgC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAElEV,OAAA;cAAGE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAG9B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YACEE,SAAS,EAAC,OAAO;YACjBE,GAAG,EAAC,oBAAoB;YACxBC,GAAG,EAAEkB;UAAiB;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eAEFV,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAwB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAE9DV,OAAA;cAAGE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAE9B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAQE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAC1BH,OAAA;YAAKE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAErDV,OAAA;YACEE,SAAS,EAAC,OAAO;YACjBE,GAAG,EAAC,qBAAqB;YACzBC,GAAG,EAAEmB;UAAoB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAKE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBH,OAAA;YAAKE,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACpBH,OAAA;gBACEE,SAAS,EAAC,OAAO;gBACjBE,GAAG,EAAC,qBAAqB;gBACzBC,GAAG,EAAEoB;cAAoB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAEFV,OAAA;gBAAKE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBH,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBH,OAAA;cAAKE,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACtBH,OAAA;gBAAKE,SAAS,EAAC,OAAO;gBAACE,GAAG,EAAC,oBAAoB;gBAACC,GAAG,EAAEqB;cAAQ;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEhEV,OAAA;gBAAKE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBH,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBH,OAAA;cAAKE,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACtBH,OAAA;gBACEE,SAAS,EAAC,OAAO;gBACjBE,GAAG,EAAC,oBAAoB;gBACxBC,GAAG,EAAEkB;cAAiB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAEFV,OAAA;gBACEE,SAAS,EAAC,KAAK;gBACfE,GAAG,EAAC,mBAAmB;gBACvBC,GAAG,EAAEsB;cAAwB;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtBH,OAAA;UAAKE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAACE,GAAG,EAAC,oBAAoB;YAACC,GAAG,EAAER;UAAM;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE9DV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEnDV,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjCH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAACE,GAAG,EAAC,qBAAqB;YAACC,GAAG,EAAEP;UAAW;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEpEV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEnDV,OAAA;cAAKE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBH,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAAAC,QAAA,eACpBH,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpCH,OAAA;kBAAKE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BH,OAAA;oBAAKE,SAAS,EAAC,QAAQ;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEuB;kBAAM;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEjDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEwB;kBAAM;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAElDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEyB;kBAAO;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEnDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAE0B;kBAAM;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAElDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAE2B;kBAAO;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEnDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAE4B;kBAAO;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEnDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAE4B;kBAAO;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEnDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAE4B;kBAAO;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAGE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7BH,OAAA;YAAME,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAE1CV,OAAA;YAAME,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAwB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAKE,SAAS,EAAC,OAAO;UAACE,GAAG,EAAC,oBAAoB;UAACC,GAAG,EAAE6B;QAAS;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEjEV,OAAA;UAAGE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UACEE,SAAS,EAAC,oBAAoB;UAC9BE,GAAG,EAAC,kBAAkB;UACtBC,GAAG,EAAE8B;QAAkC;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eAEFV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAACE,GAAG,EAAC,oBAAoB;YAACC,GAAG,EAAE+B;UAAS;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEjEV,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAGE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAE9B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJV,OAAA;cAAGE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAG9B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YACEE,SAAS,EAAC,OAAO;YACjBE,GAAG,EAAC,qBAAqB;YACzBC,GAAG,EAAEgC;UAAoB;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEFV,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAGE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAE9B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJV,OAAA;cAAGE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAG9B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCH,OAAA;YAAKE,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBH,OAAA;cAAKE,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBH,OAAA;gBAAKE,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBACpBH,OAAA;kBAAKE,SAAS,EAAC,SAAS;kBAAAC,QAAA,eACtBH,OAAA;oBAAKE,SAAS,EAAC,WAAW;oBAACE,GAAG,EAAC,SAAS;oBAACC,GAAG,EAAEiC;kBAAQ;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eAENV,OAAA;kBAAKE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eAENV,OAAA;gBAAKE,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBH,OAAA;kBACEE,SAAS,EAAC,OAAO;kBACjBE,GAAG,EAAC,qBAAqB;kBACzBC,GAAG,EAAEkC;gBAAmB;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eAEFV,OAAA;kBAAKE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENV,OAAA;cAAKE,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBH,OAAA;gBAAKE,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnCH,OAAA;kBAAKE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BH,OAAA;oBAAKE,SAAS,EAAC,WAAW;oBAACE,GAAG,EAAC,SAAS;oBAACC,GAAG,EAAEmC;kBAAS;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eAENV,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,eAC7BH,OAAA;oBAAKE,SAAS,EAAC,QAAQ;oBAACE,GAAG,EAAC,QAAQ;oBAACC,GAAG,EAAEoC;kBAAQ;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eAENV,OAAA;kBAAKE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,eAC7BH,OAAA;oBAAKE,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENV,OAAA;gBAAKE,SAAS,EAAC,OAAO;gBAAAC,QAAA,gBACpBH,OAAA;kBAAKE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAEtDV,OAAA;kBAAKE,SAAS,EAAC,OAAO;kBAAAC,QAAA,gBACpBH,OAAA;oBAAKE,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAEhDV,OAAA;oBAAKE,SAAS,EAAC,OAAO;oBAACE,GAAG,EAAC,iBAAiB;oBAACC,GAAG,EAAEqC;kBAAM;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UACEE,SAAS,EAAC,oBAAoB;UAC9BE,GAAG,EAAC,kBAAkB;UACtBC,GAAG,EAAEsC;QAAiC;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eAEFV,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YACEE,SAAS,EAAC,OAAO;YACjBE,GAAG,EAAC,kBAAkB;YACtBC,GAAG,EAAET;UAAiC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eAEFV,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAGE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJV,OAAA;cAAGE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eAC/BH,OAAA;gBAAME,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAElC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BH,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BH,OAAA;gBAAKE,SAAS,EAAC,WAAW;gBAACE,GAAG,EAAC,SAAS;gBAACC,GAAG,EAAEuC;cAAS;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAENV,OAAA;cAAKE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBH,OAAA;gBAAKE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAE9CV,OAAA;gBAAKE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEzBV,OAAA;cAAKE,SAAS,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEzBV,OAAA;cAAKE,SAAS,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEzBV,OAAA;cAAKE,SAAS,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAENV,OAAA;YAAKE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBH,OAAA;cAAKE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAE5CV,OAAA;cAAKE,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE3BV,OAAA;cAAKE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAGE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJV,OAAA;UAAQE,SAAS,EAAC,UAAU;UAAAC,QAAA,eAC1BH,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAqB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YACEE,SAAS,EAAC,gBAAgB;YAC1BE,GAAG,EAAC,cAAc;YAClBC,GAAG,EAAEwC;UAAc;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEFV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAACE,GAAG,EAAC,oBAAoB;cAACC,GAAG,EAAEyC;YAAQ;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhEV,OAAA;cAAKE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YACEE,SAAS,EAAC,gBAAgB;YAC1BE,GAAG,EAAC,cAAc;YAClBC,GAAG,EAAE0C;UAAc;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEFV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAACE,GAAG,EAAC,oBAAoB;cAACC,GAAG,EAAEyC;YAAQ;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhEV,OAAA;cAAKE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YACEE,SAAS,EAAC,gBAAgB;YAC1BE,GAAG,EAAC,cAAc;YAClBC,GAAG,EAAE2C;UAAc;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEFV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAACE,GAAG,EAAC,oBAAoB;cAACC,GAAG,EAAEyC;YAAQ;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhEV,OAAA;cAAKE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YACEE,SAAS,EAAC,gBAAgB;YAC1BE,GAAG,EAAC,cAAc;YAClBC,GAAG,EAAE4C;UAAc;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEFV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAACE,GAAG,EAAC,oBAAoB;cAACC,GAAG,EAAEyC;YAAQ;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhEV,OAAA;cAAKE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YACEE,SAAS,EAAC,gBAAgB;YAC1BE,GAAG,EAAC,cAAc;YAClBC,GAAG,EAAE6C;UAAc;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEFV,OAAA;YAAKE,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAACE,GAAG,EAAC,oBAAoB;cAACC,GAAG,EAAEyC;YAAQ;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhEV,OAAA;cAAKE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UACEE,SAAS,EAAC,OAAO;UACjBE,GAAG,EAAC,qBAAqB;UACzBC,GAAG,EAAE8C;QAAc;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEFV,OAAA;UAAGE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAmB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBH,OAAA;UAAGE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBH,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCH,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BH,OAAA;cAAKE,SAAS,EAAC,OAAO;cAAAC,QAAA,eACpBH,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpCH,OAAA;kBAAKE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BH,OAAA;oBACEE,SAAS,EAAC,WAAW;oBACrBE,GAAG,EAAC,WAAW;oBACfC,GAAG,EAAE+C;kBAAU;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eAEFV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEgD;kBAAM;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAElDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEiD;kBAAO;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEnDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEwB;kBAAM;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAElDV,OAAA;oBAAKE,SAAS,EAAC;kBAAa;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAE/BV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEkD;kBAAO;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEnDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEmD;kBAAO;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAEnDV,OAAA;oBAAKE,SAAS,EAAC,SAAS;oBAACE,GAAG,EAAC,MAAM;oBAACC,GAAG,EAAEwB;kBAAM;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENV,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAGE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAI/B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJV,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBH,OAAA;gBAAKE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAElDV,OAAA;gBAAKE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBH,OAAA;YAAKE,SAAS,EAAC,SAAS;YAAAC,QAAA,gBACtBH,OAAA;cAAKE,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE3BV,OAAA;cAAKE,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE3BV,OAAA;cAAKE,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAENV,OAAA;YAAKE,SAAS,EAAC,KAAK;YAACE,GAAG,EAAC,SAAS;YAACC,GAAG,EAAEoD;UAAQ;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCH,OAAA;UAAKE,SAAS,EAAC,KAAK;UAACE,GAAG,EAAC,eAAe;UAACC,GAAG,EAAEqD;QAAc;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE/DV,OAAA;UAAGE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAErDV,OAAA;YAAME,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEJV,OAAA;UAAGE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAA6B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAENV,OAAA;QAAKE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBH,OAAA;UAAKE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBH,OAAA;YAAKE,SAAS,EAAC,OAAO;YAACE,GAAG,EAAC,oBAAoB;YAACC,GAAG,EAAEX;UAAQ;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEhEV,OAAA;YAAKE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAG;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBH,OAAA;YAAKE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAE1DV,OAAA;YAAKE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAENV,OAAA;UAAKE,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBH,OAAA;YAAGE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAE9B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACiD,EAAA,GA7nBW1D,WAAW;AA+nBxB,eAAeA,WAAW;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}