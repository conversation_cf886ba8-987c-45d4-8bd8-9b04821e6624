{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Projects/github.com/Izy-Mercado/izy-lp/src/LandingPageV200/LandingPageV200.jsx\";\nimport basket2 from \"./basket-2.svg\";\nimport chatgptImage10DeJulDe20251146051 from \"./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png\";\nimport iPhone1 from \"./i-phone.png\";\nimport iPhone2 from \"./i-phone-2.png\";\nimport iPhone3 from \"./i-phone-3.png\";\nimport apple from \"./apple.svg\";\nimport googlePlay from \"./google-play.svg\";\nimport \"./styleNew.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LandingPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"landing-page-new\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"browser-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"title-bar-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"close-button\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"minimize-button\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"zoom-button\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"address-bar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"url-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"security-icon\",\n            children: \"\\uD83D\\uDD12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"url-text\",\n            children: \"izy.com.br\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header-new\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"nav-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-new\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"logo-icon\",\n            alt: \"izy\",\n            src: basket2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"izy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"nav-link\",\n            children: \"Seja nosso parceiro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"nav-link\",\n            children: \"Suporte\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"cta-button\",\n          children: \"Quero fazer minhas compras com a Izy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-text\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: [\"Sua compra \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"highlight\",\n              children: \"ficou inteligente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 26\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-image-container\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"hero-image\",\n            alt: \"Mulher usando smartphone para compras\",\n            src: chatgptImage10DeJulDe20251146051\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-cta\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"hero-cta-button\",\n          children: \"Quero fazer minhas compras com a Izy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"app-showcase\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"showcase-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"phones-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-mockup\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: iPhone1,\n              alt: \"App interface - Crie sua lista\",\n              className: \"phone-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"phone-icon\",\n                children: \"\\u2728\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"phone-text\",\n                children: \"Crie sua lista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-mockup\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: iPhone2,\n              alt: \"App interface - Compare pre\\xE7os\",\n              className: \"phone-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDED2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"phone-text\",\n                children: \"Cote os melhores pre\\xE7os\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-mockup\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: iPhone3,\n              alt: \"App interface - Ganhe cashback\",\n              className: \"phone-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDCB0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"phone-text\",\n                children: \"Ganhe cashback\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"features-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"features-title\",\n          children: \"Como funciona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCDD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: \"Crie sua lista\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: \"Monte sua lista de compras de forma r\\xE1pida e intuitiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: \"Compare pre\\xE7os\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: \"Encontre os melhores pre\\xE7os nos mercados da sua regi\\xE3o\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDE9A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: \"Receba em casa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: \"Suas compras chegam rapidinho na sua porta\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCB8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"feature-title\",\n              children: \"Ganhe cashback\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"feature-description\",\n              children: \"Receba dinheiro de volta a cada compra realizada\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"cta-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cta-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"cta-title\",\n          children: \"Pronto para economizar?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"cta-subtitle\",\n          children: \"Baixe o app da Izy e comece a fazer compras inteligentes hoje mesmo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"app-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"app-button\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: apple,\n              alt: \"Download na App Store\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-small\",\n                children: \"Baixe na\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-large\",\n                children: \"App Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"app-button\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: googlePlay,\n              alt: \"Dispon\\xEDvel no Google Play\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"button-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-small\",\n                children: \"Dispon\\xEDvel no\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"button-large\",\n                children: \"Google Play\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer-new\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            className: \"footer-logo-icon\",\n            alt: \"izy\",\n            src: basket2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"footer-logo-text\",\n            children: \"izy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-description\",\n          children: \"O futuro das compras \\xE9 local, digital e inteligente. Venha com a gente!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"footer-link\",\n            children: \"Seja um parceiro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"footer-link\",\n            children: \"Termos de uso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"footer-link\",\n            children: \"Privacidade\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            className: \"footer-link\",\n            children: \"Suporte\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["basket2", "chatgptImage10DeJulDe20251146051", "iPhone1", "iPhone2", "iPhone3", "apple", "googlePlay", "jsxDEV", "_jsxDEV", "LandingPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alt", "src", "href", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Projects/github.com/Izy-Mercado/izy-lp/src/LandingPageV200/LandingPageV200.jsx"], "sourcesContent": ["import basket2 from \"./basket-2.svg\";\nimport chatgptImage10DeJulDe20251146051 from \"./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png\";\nimport iPhone1 from \"./i-phone.png\";\nimport iPhone2 from \"./i-phone-2.png\";\nimport iPhone3 from \"./i-phone-3.png\";\nimport apple from \"./apple.svg\";\nimport googlePlay from \"./google-play.svg\";\nimport \"./styleNew.css\";\n\nexport const LandingPage = () => {\n  return (\n    <div className=\"landing-page-new\">\n      {/* Browser Header */}\n      <div className=\"browser-header\">\n        <div className=\"title-bar-buttons\">\n          <div className=\"close-button\"></div>\n          <div className=\"minimize-button\"></div>\n          <div className=\"zoom-button\"></div>\n        </div>\n        <div className=\"address-bar\">\n          <div className=\"url-container\">\n            <div className=\"security-icon\">🔒</div>\n            <span className=\"url-text\">izy.com.br</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Header */}\n      <header className=\"header-new\">\n        <nav className=\"nav-container\">\n          <div className=\"logo-new\">\n            <img className=\"logo-icon\" alt=\"izy\" src={basket2} />\n            <span className=\"logo-text\">izy</span>\n          </div>\n          <div className=\"nav-links\">\n            <a href=\"#\" className=\"nav-link\">Seja nosso parceiro</a>\n            <a href=\"#\" className=\"nav-link\">Suporte</a>\n          </div>\n          <button className=\"cta-button\">\n            Quero fazer minhas compras com a Izy\n          </button>\n        </nav>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"hero-section\">\n        <div className=\"hero-content\">\n          <div className=\"hero-text\">\n            <h1 className=\"hero-title\">\n              Sua compra <span className=\"highlight\">ficou inteligente</span>\n            </h1>\n          </div>\n          <div className=\"hero-image-container\">\n            <img\n              className=\"hero-image\"\n              alt=\"Mulher usando smartphone para compras\"\n              src={chatgptImage10DeJulDe20251146051}\n            />\n          </div>\n        </div>\n        <div className=\"hero-cta\">\n          <button className=\"hero-cta-button\">\n            Quero fazer minhas compras com a Izy\n          </button>\n        </div>\n      </section>\n\n      {/* Mobile App Showcase */}\n      <section className=\"app-showcase\">\n        <div className=\"showcase-content\">\n          <div className=\"phones-container\">\n            <div className=\"phone-mockup\">\n              <img src={iPhone1} alt=\"App interface - Crie sua lista\" className=\"phone-image\" />\n              <div className=\"phone-title\">\n                <span className=\"phone-icon\">✨</span>\n                <span className=\"phone-text\">Crie sua lista</span>\n              </div>\n            </div>\n            <div className=\"phone-mockup\">\n              <img src={iPhone2} alt=\"App interface - Compare preços\" className=\"phone-image\" />\n              <div className=\"phone-title\">\n                <span className=\"phone-icon\">🛒</span>\n                <span className=\"phone-text\">Cote os melhores preços</span>\n              </div>\n            </div>\n            <div className=\"phone-mockup\">\n              <img src={iPhone3} alt=\"App interface - Ganhe cashback\" className=\"phone-image\" />\n              <div className=\"phone-title\">\n                <span className=\"phone-icon\">💰</span>\n                <span className=\"phone-text\">Ganhe cashback</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"features-section\">\n        <div className=\"features-content\">\n          <h2 className=\"features-title\">Como funciona</h2>\n          <div className=\"features-grid\">\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">📝</div>\n              <h3 className=\"feature-title\">Crie sua lista</h3>\n              <p className=\"feature-description\">\n                Monte sua lista de compras de forma rápida e intuitiva\n              </p>\n            </div>\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">💰</div>\n              <h3 className=\"feature-title\">Compare preços</h3>\n              <p className=\"feature-description\">\n                Encontre os melhores preços nos mercados da sua região\n              </p>\n            </div>\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">🚚</div>\n              <h3 className=\"feature-title\">Receba em casa</h3>\n              <p className=\"feature-description\">\n                Suas compras chegam rapidinho na sua porta\n              </p>\n            </div>\n            <div className=\"feature-item\">\n              <div className=\"feature-icon\">💸</div>\n              <h3 className=\"feature-title\">Ganhe cashback</h3>\n              <p className=\"feature-description\">\n                Receba dinheiro de volta a cada compra realizada\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"cta-section\">\n        <div className=\"cta-content\">\n          <h2 className=\"cta-title\">Pronto para economizar?</h2>\n          <p className=\"cta-subtitle\">\n            Baixe o app da Izy e comece a fazer compras inteligentes hoje mesmo\n          </p>\n          <div className=\"app-buttons\">\n            <a href=\"#\" className=\"app-button\">\n              <img src={apple} alt=\"Download na App Store\" />\n              <div className=\"button-text\">\n                <span className=\"button-small\">Baixe na</span>\n                <span className=\"button-large\">App Store</span>\n              </div>\n            </a>\n            <a href=\"#\" className=\"app-button\">\n              <img src={googlePlay} alt=\"Disponível no Google Play\" />\n              <div className=\"button-text\">\n                <span className=\"button-small\">Disponível no</span>\n                <span className=\"button-large\">Google Play</span>\n              </div>\n            </a>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"footer-new\">\n        <div className=\"footer-content\">\n          <div className=\"footer-logo\">\n            <img className=\"footer-logo-icon\" alt=\"izy\" src={basket2} />\n            <span className=\"footer-logo-text\">izy</span>\n          </div>\n          <p className=\"footer-description\">\n            O futuro das compras é local, digital e inteligente. Venha com a gente!\n          </p>\n          <div className=\"footer-links\">\n            <a href=\"#\" className=\"footer-link\">Seja um parceiro</a>\n            <a href=\"#\" className=\"footer-link\">Termos de uso</a>\n            <a href=\"#\" className=\"footer-link\">Privacidade</a>\n            <a href=\"#\" className=\"footer-link\">Suporte</a>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,OAAOC,gCAAgC,MAAM,kDAAkD;AAC/F,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC/B,oBACED,OAAA;IAAKE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/BH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAKE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCP,OAAA;UAAKE,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCP,OAAA;UAAKE,SAAS,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BH,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BH,OAAA;YAAKE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCP,OAAA;YAAME,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNP,OAAA;MAAQE,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAKE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBH,OAAA;YAAKE,SAAS,EAAC,WAAW;YAACM,GAAG,EAAC,KAAK;YAACC,GAAG,EAAEjB;UAAQ;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDP,OAAA;YAAME,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxDP,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNP,OAAA;UAAQE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAE/B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTP,OAAA;MAASE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC/BH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBH,OAAA;YAAIE,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,aACd,eAAAH,OAAA;cAAME,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCH,OAAA;YACEE,SAAS,EAAC,YAAY;YACtBM,GAAG,EAAC,uCAAuC;YAC3CC,GAAG,EAAEhB;UAAiC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBH,OAAA;UAAQE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEpC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC/BH,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BH,OAAA;UAAKE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BH,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKS,GAAG,EAAEf,OAAQ;cAACc,GAAG,EAAC,gCAAgC;cAACN,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClFP,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BH,OAAA;gBAAME,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCP,OAAA;gBAAME,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKS,GAAG,EAAEd,OAAQ;cAACa,GAAG,EAAC,mCAAgC;cAACN,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClFP,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BH,OAAA;gBAAME,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCP,OAAA;gBAAME,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKS,GAAG,EAAEb,OAAQ;cAACY,GAAG,EAAC,gCAAgC;cAACN,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClFP,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BH,OAAA;gBAAME,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCP,OAAA;gBAAME,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCH,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BH,OAAA;UAAIE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjDP,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BH,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCP,OAAA;cAAIE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDP,OAAA;cAAGE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCP,OAAA;cAAIE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDP,OAAA;cAAGE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCP,OAAA;cAAIE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDP,OAAA;cAAGE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BH,OAAA;cAAKE,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtCP,OAAA;cAAIE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDP,OAAA;cAAGE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEnC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC9BH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAIE,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDP,OAAA;UAAGE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BH,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAChCH,OAAA;cAAKS,GAAG,EAAEZ,KAAM;cAACW,GAAG,EAAC;YAAuB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CP,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BH,OAAA;gBAAME,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CP,OAAA;gBAAME,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACJP,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAChCH,OAAA;cAAKS,GAAG,EAAEX,UAAW;cAACU,GAAG,EAAC;YAA2B;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDP,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BH,OAAA;gBAAME,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDP,OAAA;gBAAME,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAAQE,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5BH,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BH,OAAA;YAAKE,SAAS,EAAC,kBAAkB;YAACM,GAAG,EAAC,KAAK;YAACC,GAAG,EAAEjB;UAAQ;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DP,OAAA;YAAME,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNP,OAAA;UAAGE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BH,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxDP,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrDP,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnDP,OAAA;YAAGU,IAAI,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACI,EAAA,GA1KWV,WAAW;AA4KxB,eAAeA,WAAW;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}