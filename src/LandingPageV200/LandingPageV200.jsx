import basket2 from "./basket-2.svg";
import chatgptImage10DeJulDe20251146051 from "./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png";
import apple from "./apple.svg";
import googlePlay from "./google-play.svg";
import "./styleNew.css";

export const LandingPageNew = () => {
  return (
    <div className="landing-page-new">
      {/* Header */}
      <header className="header-new">
        <nav className="nav-container">
          <div className="logo-new">
            <img className="logo-icon" alt="izy" src={basket2} />
            <span className="logo-text">izy</span>
          </div>
          <div className="nav-links">
            <a href="#" className="nav-link">Seja nosso parceiro</a>
            <a href="#" className="nav-link">Suporte</a>
          </div>
          <button className="cta-button">
            Quero fazer minhas compras com a Izy
          </button>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Sua compra <span className="highlight">ficou inteligente</span>
            </h1>
            <p className="hero-subtitle">
              Compare preços, economize tempo e receba em casa com a Izy
            </p>
          </div>
          <div className="hero-image-container">
            <img 
              className="hero-image" 
              alt="Mulher usando smartphone para compras" 
              src={chatgptImage10DeJulDe20251146051} 
            />
          </div>
        </div>
      </section>

      {/* Mobile App Showcase */}
      <section className="app-showcase">
        <div className="showcase-content">
          <h2 className="showcase-title">Baixe o app e comece a economizar</h2>
          <div className="phones-container">
            <div className="phone-mockup phone-1">
              <div className="phone-screen">
                <div className="app-interface">
                  <div className="app-header">
                    <span className="app-title">Lista de Compras</span>
                  </div>
                  <div className="app-content">
                    <div className="product-item">
                      <span>Arroz 5kg</span>
                      <span className="price">R$ 18,90</span>
                    </div>
                    <div className="product-item">
                      <span>Feijão 1kg</span>
                      <span className="price">R$ 7,50</span>
                    </div>
                    <div className="product-item">
                      <span>Óleo de Soja</span>
                      <span className="price">R$ 4,20</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="phone-mockup phone-2">
              <div className="phone-screen">
                <div className="app-interface">
                  <div className="app-header">
                    <span className="app-title">Comparar Preços</span>
                  </div>
                  <div className="app-content">
                    <div className="store-comparison">
                      <div className="store-item">
                        <span className="store-name">Mercado A</span>
                        <span className="total-price">R$ 30,60</span>
                      </div>
                      <div className="store-item best-price">
                        <span className="store-name">Mercado B</span>
                        <span className="total-price">R$ 28,90</span>
                      </div>
                      <div className="store-item">
                        <span className="store-name">Mercado C</span>
                        <span className="total-price">R$ 32,10</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="phone-mockup phone-3">
              <div className="phone-screen">
                <div className="app-interface">
                  <div className="app-header">
                    <span className="app-title">Pedido Confirmado</span>
                  </div>
                  <div className="app-content">
                    <div className="order-status">
                      <div className="status-icon">✓</div>
                      <p>Seu pedido foi confirmado!</p>
                      <p className="delivery-time">Entrega em 2 horas</p>
                      <div className="cashback-info">
                        <span>Cashback: R$ 2,89</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="features-content">
          <h2 className="features-title">Como funciona</h2>
          <div className="features-grid">
            <div className="feature-item">
              <div className="feature-icon">📝</div>
              <h3 className="feature-title">Crie sua lista</h3>
              <p className="feature-description">
                Monte sua lista de compras de forma rápida e intuitiva
              </p>
            </div>
            <div className="feature-item">
              <div className="feature-icon">💰</div>
              <h3 className="feature-title">Compare preços</h3>
              <p className="feature-description">
                Encontre os melhores preços nos mercados da sua região
              </p>
            </div>
            <div className="feature-item">
              <div className="feature-icon">🚚</div>
              <h3 className="feature-title">Receba em casa</h3>
              <p className="feature-description">
                Suas compras chegam rapidinho na sua porta
              </p>
            </div>
            <div className="feature-item">
              <div className="feature-icon">💸</div>
              <h3 className="feature-title">Ganhe cashback</h3>
              <p className="feature-description">
                Receba dinheiro de volta a cada compra realizada
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-content">
          <h2 className="cta-title">Pronto para economizar?</h2>
          <p className="cta-subtitle">
            Baixe o app da Izy e comece a fazer compras inteligentes hoje mesmo
          </p>
          <div className="app-buttons">
            <a href="#" className="app-button">
              <img src={apple} alt="Download na App Store" />
              <div className="button-text">
                <span className="button-small">Baixe na</span>
                <span className="button-large">App Store</span>
              </div>
            </a>
            <a href="#" className="app-button">
              <img src={googlePlay} alt="Disponível no Google Play" />
              <div className="button-text">
                <span className="button-small">Disponível no</span>
                <span className="button-large">Google Play</span>
              </div>
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer-new">
        <div className="footer-content">
          <div className="footer-logo">
            <img className="footer-logo-icon" alt="izy" src={basket2} />
            <span className="footer-logo-text">izy</span>
          </div>
          <p className="footer-description">
            O futuro das compras é local, digital e inteligente. Venha com a gente!
          </p>
          <div className="footer-links">
            <a href="#" className="footer-link">Seja um parceiro</a>
            <a href="#" className="footer-link">Termos de uso</a>
            <a href="#" className="footer-link">Privacidade</a>
            <a href="#" className="footer-link">Suporte</a>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPageNew;
