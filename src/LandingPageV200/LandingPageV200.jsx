import basket2 from "./basket-2.svg";
import chatgptImage10DeJulDe20251146051 from "./chatgpt-image-10-de-jul-de-2025-11-46-05-1.png";
import iPhone1 from "./i-phone.png";
import iPhone2 from "./i-phone-2.png";
import iPhone3 from "./i-phone-3.png";
import starsIcon from "./stars-light-sparkle-1-2.svg";
import basketIcon from "./basket-4.png";
import dollarIcon from "./dollar-front-color.png";
import apple from "./apple.svg";
import googlePlay from "./google-play.svg";
import "./styleNew.css";

export const LandingPage = () => {
  return (
    <div className="landing-page-new">
      {/* Browser Header */}
      <div className="browser-header">
        <div className="title-bar-buttons">
          <div className="close-button"></div>
          <div className="minimize-button"></div>
          <div className="zoom-button"></div>
        </div>
        <div className="address-bar">
          <div className="url-container">
            <div className="security-icon">🔒</div>
            <span className="url-text">izy.com.br</span>
          </div>
        </div>
      </div>

      {/* Header */}
      <header className="header-new">
        <nav className="nav-container">
          <div className="logo-new">
            <img className="logo-icon" alt="izy" src={basket2} />
            <span className="logo-text">izy</span>
          </div>
          <div className="nav-links">
            <a href="#" className="nav-link">Seja nosso parceiro</a>
            <a href="#" className="nav-link">Suporte</a>
          </div>
          <button className="cta-button">
            Quero fazer minhas compras com a Izy
          </button>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Sua compra <span className="highlight">ficou inteligente</span>
            </h1>
          </div>
          <div className="hero-image-container">
            <img
              className="hero-image"
              alt="Mulher usando smartphone para compras"
              src={chatgptImage10DeJulDe20251146051}
            />
          </div>
        </div>
        <div className="hero-cta">
          <button className="hero-cta-button">
            Quero fazer minhas compras com a Izy
          </button>
        </div>
      </section>

      {/* Mobile App Showcase */}
      <section className="app-showcase">
        <div className="showcase-content">
          <div className="phones-container">
            <div className="phone-mockup">
              <img src={iPhone1} alt="App interface - Crie sua lista" className="phone-image" />
              <div className="phone-title">
                <span className="phone-icon">✨</span>
                <span className="phone-text">Crie sua lista</span>
              </div>
            </div>
            <div className="phone-mockup">
              <img src={iPhone2} alt="App interface - Compare preços" className="phone-image" />
              <div className="phone-title">
                <span className="phone-icon">🛒</span>
                <span className="phone-text">Cote os melhores preços</span>
              </div>
            </div>
            <div className="phone-mockup">
              <img src={iPhone3} alt="App interface - Ganhe cashback" className="phone-image" />
              <div className="phone-title">
                <span className="phone-icon">💰</span>
                <span className="phone-text">Ganhe cashback</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="content-section">
        <div className="content-container">
          <div className="content-left">
            <img src={basketIcon} alt="Shopping basket" className="content-icon" />
            <p className="content-text">
              Receba seus produtos fresquinhos, baixe nosso app!
            </p>
          </div>
          <div className="content-right">
            <img
              src={chatgptImage10DeJulDe20251146051}
              alt="App demonstration"
              className="content-image"
            />
          </div>
        </div>
      </section>

      {/* Download Section */}
      <section className="download-section">
        <div className="download-content">
          <h2 className="download-title">Izy Mercado para todos os momentos!</h2>
          <div className="app-buttons">
            <a href="#" className="app-button">
              <img src={apple} alt="Download na App Store" />
              <div className="button-text">
                <span className="button-small">Disponível na</span>
                <span className="button-large">Apple Store</span>
              </div>
            </a>
            <a href="#" className="app-button">
              <img src={googlePlay} alt="Disponível no Google Play" />
              <div className="button-text">
                <span className="button-small">Disponível na</span>
                <span className="button-large">Google Play</span>
              </div>
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer-new">
        <div className="footer-content">
          <div className="footer-top">
            <div className="footer-left">
              <div className="footer-logo">
                <img className="footer-logo-icon" alt="izy" src={basket2} />
                <span className="footer-logo-text">izy</span>
              </div>
              <p className="footer-description">
                O futuro das compras é local, digital e inteligente. Venha com a gente!
              </p>
            </div>
            <div className="footer-right">
              <div className="footer-links">
                <a href="#" className="footer-link">Seja um parceiro</a>
                <a href="#" className="footer-link">Termos de uso</a>
                <a href="#" className="footer-link">Privacidade</a>
              </div>
              <div className="app-buttons">
                <a href="#" className="app-button">
                  <img src={apple} alt="Download na App Store" />
                  <div className="button-text">
                    <span className="button-small">Disponível na</span>
                    <span className="button-large">Apple Store</span>
                  </div>
                </a>
                <a href="#" className="app-button">
                  <img src={googlePlay} alt="Disponível no Google Play" />
                  <div className="button-text">
                    <span className="button-small">Disponível na</span>
                    <span className="button-large">Google Play</span>
                  </div>
                </a>
              </div>
            </div>
          </div>
          <div className="footer-bottom">
            <p className="footer-address">
              Av. Engenheiro Roberto Freire, 1962, Swaway Shopping, Loja 13, Capim Macio, Natal/RN, 59082-095
            </p>
            <div className="footer-company">
              <p className="footer-copyright">© 2025, Izy Mercado. Todos os direitos reservados</p>
              <span className="footer-cnpj">61.134.691/0001-00</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
