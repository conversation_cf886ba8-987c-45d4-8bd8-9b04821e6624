/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}

.landing-page-new {
  min-height: 100vh;
  background: linear-gradient(135deg, #b771e5 0%, #ec9191 100%);
}

/* Header */
.header-new {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
}

.logo-new {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #b771e5;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #b771e5;
}

.cta-button {
  background: linear-gradient(135deg, #b771e5 0%, #ec9191 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
  padding: 8rem 2rem 4rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.highlight {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
}

.hero-image-container {
  display: flex;
  justify-content: center;
}

.hero-image {
  max-width: 100%;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* App Showcase */
.app-showcase {
  background: white;
  padding: 6rem 2rem;
}

.showcase-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.showcase-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 4rem;
}

.phones-container {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.phone-mockup {
  width: 280px;
  height: 560px;
  background: #000;
  border-radius: 30px;
  padding: 20px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 20px;
  overflow: hidden;
}

.app-interface {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.app-header {
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 1rem;
}

.app-title {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.app-content {
  flex: 1;
}

.product-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.price {
  font-weight: 600;
  color: #667eea;
}

.store-comparison {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.store-item {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid #eee;
  border-radius: 8px;
}

.best-price {
  background: #f0f8ff;
  border-color: #667eea;
}

.total-price {
  font-weight: 600;
  color: #333;
}

.order-status {
  text-align: center;
  padding: 2rem 0;
}

.status-icon {
  font-size: 3rem;
  color: #4caf50;
  margin-bottom: 1rem;
}

.delivery-time {
  color: #666;
  margin: 1rem 0;
}

.cashback-info {
  background: #e8f5e8;
  padding: 0.75rem;
  border-radius: 8px;
  margin-top: 1rem;
  color: #4caf50;
  font-weight: 600;
}

/* Features Section */
.features-section {
  background: #f8f9fa;
  padding: 6rem 2rem;
}

.features-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.features-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 4rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
}

.feature-item {
  text-align: center;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.feature-description {
  color: #666;
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 6rem 2rem;
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
}

.cta-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
}

.app-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.app-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  text-decoration: none;
  color: white;
  transition: transform 0.3s ease;
}

.app-button:hover {
  transform: translateY(-2px);
}

.app-button img {
  width: 24px;
  height: 24px;
}

.button-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.button-small {
  font-size: 0.75rem;
  opacity: 0.8;
}

.button-large {
  font-size: 1rem;
  font-weight: 600;
}

/* Footer */
.footer-new {
  background: #333;
  color: white;
  padding: 3rem 2rem 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.footer-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.footer-logo-icon {
  width: 32px;
  height: 32px;
}

.footer-logo-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: #667eea;
}

.footer-description {
  color: #ccc;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.footer-link {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-new {
    padding: 0.5rem 0;
  }

  .nav-container {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-links {
    order: 2;
  }

  .cta-button {
    order: 1;
    margin-top: 1rem;
  }

  .hero-section {
    padding: 6rem 1rem 2rem;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .app-showcase {
    padding: 4rem 1rem;
  }

  .showcase-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .phones-container {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .phone-mockup {
    width: 240px;
    height: 480px;
  }

  .features-section {
    padding: 4rem 1rem;
  }

  .features-title {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .cta-section {
    padding: 4rem 1rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-subtitle {
    font-size: 1.1rem;
  }

  .app-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .footer-new {
    padding: 2rem 1rem 1rem;
  }

  .footer-links {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .phone-mockup {
    width: 200px;
    height: 400px;
  }

  .showcase-title,
  .features-title,
  .cta-title {
    font-size: 1.75rem;
  }
}
