/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", "SF Pro Display", -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: #333;
}

.landing-page-new {
  min-height: 100vh;
  background: linear-gradient(180deg, #B771E5 0%, #EC9191 100%);
}

/* Browser Header */
.browser-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #363435;
  border-radius: 10px 10px 0px 0px;
  box-shadow: inset 0px -0.5px 0px #000000;
  display: flex;
  align-items: center;
  gap: 74.5px;
  padding: 12px 20px;
  z-index: 1001;
  height: 52px;
}

.title-bar-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.close-button,
.minimize-button,
.zoom-button {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  border: 0.6px solid rgba(0, 0, 0, 0.1);
}

.close-button {
  background-color: #ed6a5e;
}

.minimize-button {
  background-color: #f4bf4e;
}

.zoom-button {
  background-color: #62c554;
}

.address-bar {
  flex: 1;
  display: flex;
  justify-content: center;
}

.url-container {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 6px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.security-icon {
  font-size: 12px;
}

.url-text {
  color: white;
  font-size: 13px;
  font-weight: 500;
  font-family: "SF Pro Display", sans-serif;
}

/* Header */
.header-new {
  position: fixed;
  top: 52px;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(16px);
  z-index: 1000;
  padding: 24px 0;
  width: 100%;
}

.nav-container {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 42px;
}

.logo-new {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: 32px;
  font-weight: 500;
  color: #858082;
  font-family: "Helvetica Neue", Helvetica, sans-serif;
  line-height: normal;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #858082;
  font-weight: 400;
  font-size: 16px;
  line-height: normal;
  font-family: "Roboto", sans-serif;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #858082;
}

.cta-button {
  background: linear-gradient(180deg, #B771E5 0%, #EC9191 100%);
  color: white;
  border: none;
  padding: 8px 32px;
  border-radius: 52px;
  font-weight: 500;
  font-size: 14px;
  line-height: 15.4px;
  cursor: pointer;
  transition: transform 0.3s ease;
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cta-button:hover {
  transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
  padding: 200px 42px 100px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1280px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 56px;
  font-weight: 300;
  color: white;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  font-family: "Roboto", sans-serif;
}

.highlight {
  font-weight: 700;
  color: white;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
}

.hero-image-container {
  display: flex;
  justify-content: center;
}

.hero-image {
  max-width: 100%;
  height: auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.hero-cta {
  text-align: center;
  margin-top: 2rem;
}

.hero-cta-button {
  background: linear-gradient(180deg, #B771E5 0%, #EC9191 100%);
  color: white;
  border: none;
  padding: 8px 32px;
  border-radius: 52px;
  font-weight: 500;
  font-size: 14px;
  line-height: 15.4px;
  cursor: pointer;
  transition: transform 0.3s ease;
  height: 52px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: "Roboto", sans-serif;
}

/* App Showcase */
.app-showcase {
  background: white;
  padding: 6rem 42px;
}

.showcase-content {
  max-width: 1280px;
  margin: 0 auto;
  text-align: center;
}

.phones-container {
  display: flex;
  justify-content: center;
  gap: 32px;
  align-items: center;
}

.phone-mockup {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.phone-image {
  width: 329px;
  height: 535px;
  object-fit: contain;
}

.phone-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.phone-icon {
  font-size: 16px;
}

.phone-text {
  font-size: 16px;
  font-weight: 300;
  color: #514D4F;
  font-family: "Roboto", sans-serif;
  letter-spacing: -0.32px;
  line-height: 20px;
}



/* Content Section */
.content-section {
  background: white;
  padding: 6rem 42px;
}

.content-container {
  max-width: 1280px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.content-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2rem;
}

.content-icon {
  width: 42px;
  height: 42px;
}

.content-text {
  font-size: 24px;
  font-weight: 300;
  color: #514D4F;
  font-family: "Roboto", sans-serif;
  line-height: 1.4;
  margin: 0;
}

.content-right {
  display: flex;
  justify-content: center;
}

.content-image {
  max-width: 100%;
  height: auto;
  border-radius: 20px;
}

/* Download Section */
.download-section {
  background: white;
  padding: 6rem 42px;
  text-align: center;
}

.download-content {
  max-width: 1280px;
  margin: 0 auto;
}

.download-title {
  font-size: 32px;
  font-weight: 300;
  color: #514D4F;
  font-family: "Roboto", sans-serif;
  margin-bottom: 3rem;
}

.app-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.app-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  border: 1px solid #EBEBE9;
  border-radius: 8px;
  padding: 16px;
  text-decoration: none;
  color: #333;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-button:hover {
  transform: translateY(-2px);
}

.app-button img {
  width: 32px;
  height: 32px;
}

.button-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.button-small {
  font-size: 12px;
  color: #666;
  font-family: "SF Pro Display", sans-serif;
}

.button-large {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  font-family: "SF Pro Display", sans-serif;
}

/* Footer */
.footer-new {
  background: #F8F8F7;
  padding: 48px 42px 32px;
}

.footer-content {
  max-width: 1280px;
  margin: 0 auto;
}

.footer-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 48px;
}

.footer-left {
  flex: 1;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.footer-logo-icon {
  width: 32px;
  height: 32px;
}

.footer-logo-text {
  font-size: 32px;
  font-weight: 500;
  color: #858082;
  font-family: "Helvetica Neue", sans-serif;
}

.footer-description {
  color: #514D4F;
  font-size: 16px;
  font-family: "Roboto", sans-serif;
  line-height: 1.6;
  max-width: 400px;
}

.footer-right {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.footer-links {
  display: flex;
  gap: 32px;
}

.footer-link {
  color: #514D4F;
  text-decoration: none;
  font-size: 12px;
  font-family: "SF Pro Display", sans-serif;
  font-weight: 500;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #858082;
}

.footer-bottom {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #EBEBE9;
}

.footer-address {
  color: #514D4F;
  font-size: 14px;
  font-family: "Roboto", sans-serif;
  margin-bottom: 16px;
}

.footer-company {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.footer-copyright {
  color: #514D4F;
  font-size: 14px;
  font-family: "Roboto", sans-serif;
  margin: 0;
}

.footer-cnpj {
  color: #514D4F;
  font-size: 14px;
  font-family: "Roboto", sans-serif;
}

/* Responsive Design */
@media (max-width: 768px) {
  .browser-header {
    padding: 8px 16px;
  }

  .header-new {
    padding: 16px 0;
  }

  .nav-container {
    flex-direction: column;
    gap: 1rem;
    padding: 0 20px;
  }

  .nav-links {
    order: 2;
  }

  .cta-button {
    order: 1;
    margin-top: 1rem;
  }

  .hero-section {
    padding: 150px 20px 50px;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .hero-title {
    font-size: 40px;
  }

  .app-showcase {
    padding: 4rem 20px;
  }

  .phones-container {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  .phone-image {
    width: 250px;
    height: 400px;
  }

  .content-section {
    padding: 4rem 20px;
  }

  .content-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .content-left {
    align-items: center;
  }

  .download-section {
    padding: 4rem 20px;
  }

  .download-title {
    font-size: 24px;
  }

  .app-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .footer-new {
    padding: 32px 20px 24px;
  }

  .footer-top {
    flex-direction: column;
    gap: 2rem;
  }

  .footer-right {
    align-items: center;
  }

  .footer-links {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-company {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 32px;
  }

  .phone-image {
    width: 200px;
    height: 320px;
  }

  .content-text {
    font-size: 20px;
  }

  .download-title {
    font-size: 20px;
  }
}
