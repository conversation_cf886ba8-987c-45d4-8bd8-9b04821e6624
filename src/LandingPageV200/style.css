.landing-page {
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}

.landing-page .landing-page-v {
  background-color: #ffffff;
  height: 4690px;
  position: relative;
  width: 1280px;
}

.landing-page .footer {
  align-items: center;
  background-color: #f8f8f7;
  display: flex;
  flex-direction: column;
  gap: 48px;
  height: 393px;
  justify-content: center;
  left: 0;
  padding: 32px 42px;
  position: absolute;
  top: 4297px;
  width: 1280px;
}

.landing-page .div {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.landing-page .div-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 16px;
  position: relative;
}

.landing-page .div-3 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 4px;
  position: relative;
}

.landing-page .shopping-ecommerce {
  height: 62px;
  position: relative;
  width: 62px;
}

.landing-page .text-wrapper {
  color: #858082;
  font-family: "Helvetica Neue-Medium", Helvetica;
  font-size: 62px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -0.50px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .p {
  color: #514d4f;
  font-family: "Roboto-Light", Helvetica;
  font-size: 20px;
  font-weight: 300;
  letter-spacing: 0;
  line-height: 22px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .img {
  flex: 0 0 auto;
  position: relative;
}

.landing-page .content {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 32px;
  position: relative;
}

.landing-page .content-right {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-content: flex-end;
  position: relative;
}

.landing-page .button {
  all: unset;
  align-items: center;
  border: 1px solid;
  border-color: #eaeaea;
  border-radius: 52px;
  box-sizing: border-box;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 12px;
  height: 36px;
  justify-content: center;
  overflow: hidden;
  padding: 16px 32px;
  position: relative;
}

.landing-page .text-wrapper-2 {
  color: #514d4f;
  font-family: "SF Pro Display-Medium", Helvetica;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 13.2px;
  margin-bottom: -3.50px;
  margin-top: -5.50px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .store {
  align-items: center;
  display: flex;
  gap: 16px;
  position: relative;
  width: 312px;
}

.landing-page .card-status-pedido {
  align-items: flex-start;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #ebebe9;
  border-radius: 8px;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 32px;
  justify-content: center;
  overflow: hidden;
  padding: 16px;
  position: relative;
}

.landing-page .img-2 {
  height: 32px;
  position: relative;
  width: 32px;
}

.landing-page .div-4 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 4px;
  position: relative;
}

.landing-page .text-wrapper-3 {
  align-self: stretch;
  color: #666666;
  font-family: "Roboto-Light", Helvetica;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
}

.landing-page .text-wrapper-4 {
  align-self: stretch;
  color: #514d4f;
  font-family: "Roboto-Regular", Helvetica;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  position: relative;
}

.landing-page .text-content {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 8px;
  justify-content: center;
  position: relative;
}

.landing-page .text-wrapper-5 {
  color: #514d4f;
  font-family: "SF Pro Text-Light", Helvetica;
  font-size: 14px;
  font-weight: 300;
  letter-spacing: 0;
  line-height: 15.4px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .company {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 8px;
  justify-content: center;
  position: relative;
}

.landing-page .pointer {
  background-color: #e5e5e4;
  border-radius: 16px;
  height: 4px;
  position: relative;
  width: 4px;
}

.landing-page .content-categorias {
  align-items: center;
  display: inline-flex;
  left: 556px;
  position: absolute;
  top: 306px;
}

.landing-page .notification {
  height: 48px;
  position: relative;
  width: 48px;
}

.landing-page .div-wrapper {
  align-items: center;
  background-color: #f8f8f7;
  border: 2px solid;
  border-color: #ffffff;
  border-radius: 42px;
  display: flex;
  flex-direction: column;
  height: 48px;
  justify-content: center;
  margin-left: -8px;
  overflow: hidden;
  padding: 10px;
  position: relative;
  width: 48px;
}

.landing-page .img-3 {
  height: 24px;
  position: relative;
  width: 24px;
}

.landing-page .group {
  height: 24px;
}

.landing-page .overlap-group {
  height: 19px;
  left: 2px;
  position: relative;
  top: 3px;
  width: 20px;
}

.landing-page .oval {
  border: 1.5px solid;
  border-color: #514d4f;
  border-radius: 2.25px;
  height: 5px;
  left: 3px;
  position: absolute;
  top: 11px;
  width: 5px;
}

.landing-page .path {
  height: 19px;
  left: 0;
  position: absolute;
  top: 0;
  width: 20px;
}

.landing-page .path-2 {
  height: 5px;
  left: 11px;
  position: absolute;
  top: 14px;
  width: 6px;
}

.landing-page .path-3 {
  height: 2px;
  left: 0;
  position: absolute;
  top: 8px;
  width: 19px;
}

.landing-page .path-4 {
  height: 5px;
  left: 8px;
  position: absolute;
  top: 8px;
  width: 6px;
}

.landing-page .overlap-group-2 {
  height: 22px;
  left: 4px;
  position: relative;
  top: 1px;
  width: 16px;
}

.landing-page .path-5 {
  height: 2px;
  left: 0;
  position: absolute;
  top: 8px;
  width: 15px;
}

.landing-page .path-6 {
  height: 19px;
  left: 8px;
  position: absolute;
  top: 3px;
  width: 5px;
}

.landing-page .path-7 {
  height: 22px;
  left: 0;
  position: absolute;
  top: 0;
  width: 16px;
}

.landing-page .path-8 {
  height: 2px;
  left: 3px;
  position: absolute;
  top: 3px;
  width: 10px;
}

.landing-page .notification-2 {
  height: 48px;
  margin-left: -8px;
  position: relative;
  width: 48px;
}

.landing-page .seu-mercado-f-cil-e {
  color: transparent;
  font-family: "Roboto-Thin", Helvetica;
  font-size: 36px;
  font-weight: 400;
  left: 391px;
  letter-spacing: 0;
  line-height: 39.6px;
  position: absolute;
  top: 395px;
  white-space: nowrap;
}

.landing-page .span {
  color: #514d4f;
  font-weight: 100;
}

.landing-page .text-wrapper-6 {
  color: #514d4f;
  font-family: "Roboto-Medium", Helvetica;
  font-weight: 500;
}

.landing-page .button-2 {
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(183, 113, 229) 0%,
    rgb(236.79, 145.71, 145.71) 100%
  );
  border-radius: 52px;
  display: inline-flex;
  gap: 12px;
  height: 52px;
  justify-content: center;
  left: 486px;
  overflow: hidden;
  padding: 8px 32px;
  position: absolute;
  top: 478px;
}

.landing-page .text-wrapper-7 {
  color: #ffffff;
  font-family: "Roboto-Medium", Helvetica;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 15.4px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .overlap {
  height: 533px;
  left: 150px;
  position: absolute;
  top: 608px;
  width: 995px;
}

.landing-page .chatgpt-image-de {
  height: 533px;
  left: 90px;
  object-fit: cover;
  position: absolute;
  top: 0;
  width: 800px;
}

.landing-page .card-status-pedido-2 {
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 4px 20px #514d4f1a;
  display: flex;
  flex-direction: column;
  gap: 32px;
  justify-content: center;
  left: 0;
  overflow: hidden;
  padding: 16px;
  position: absolute;
  top: 117px;
  width: 210px;
}

.landing-page .img-4 {
  height: 42px;
  position: relative;
  width: 42px;
}

.landing-page .content-2 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 8px;
  position: relative;
  width: 100%;
}

.landing-page .text-wrapper-8 {
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent;
  align-self: stretch;
  background: linear-gradient(
    180deg,
    rgb(183, 113, 229) 0%,
    rgb(236.79, 145.71, 145.71) 100%
  );
  background-clip: text;
  color: transparent;
  font-family: "Roboto-Regular", Helvetica;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  text-fill-color: transparent;
}

.landing-page .text-wrapper-9 {
  align-self: stretch;
  color: #666666;
  font-family: "Roboto-Light", Helvetica;
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0;
  line-height: normal;
  position: relative;
}

.landing-page .card-status-pedido-3 {
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 4px 20px #514d4f1a;
  display: flex;
  flex-direction: column;
  gap: 32px;
  justify-content: center;
  left: 785px;
  overflow: hidden;
  padding: 16px;
  position: absolute;
  top: 291px;
  width: 210px;
}

.landing-page .button-3 {
  all: unset;
  align-items: center;
  background-color: #ffffff;
  border-radius: 52px;
  box-sizing: border-box;
  display: flex;
  height: 66px;
  justify-content: space-between;
  left: 287px;
  overflow: hidden;
  padding: 4px 16px;
  position: absolute;
  top: 343px;
  width: 408px;
}

.landing-page .text-wrapper-10 {
  color: #666666;
  font-family: "Roboto-Regular", Helvetica;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .content-3 {
  align-items: center;
  display: inline-flex;
  gap: 32px;
  left: 115px;
  position: absolute;
  top: 1289px;
}

.landing-page .card {
  background-color: #f8f8f7;
  border-radius: 40px;
  height: 535px;
  overflow: hidden;
  position: relative;
  width: 329px;
}

.landing-page .title-wrapper {
  background-image: url(./i-phone-3.png);
  background-size: 100% 100%;
  height: 496px;
  position: relative;
  top: 39px;
}

.landing-page .title {
  align-items: center;
  display: inline-flex;
  gap: 8px;
  left: 106px;
  position: relative;
  top: 7px;
}

.landing-page .text-wrapper-11 {
  color: #514d4f;
  font-family: "Roboto-Light", Helvetica;
  font-size: 16px;
  font-weight: 300;
  letter-spacing: -0.32px;
  line-height: 20px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .overlap-2 {
  background-image: url(./i-phone-2.png);
  background-size: 100% 100%;
  height: 496px;
  position: relative;
  top: 39px;
}

.landing-page .title-2 {
  align-items: center;
  display: inline-flex;
  gap: 8px;
  left: 65px;
  position: relative;
  top: 7px;
}

.landing-page .overlap-3 {
  background-image: url(./i-phone.png);
  background-size: 100% 100%;
  height: 496px;
  position: relative;
  top: 39px;
}

.landing-page .title-3 {
  align-items: center;
  display: inline-flex;
  gap: 8px;
  left: 48px;
  position: relative;
  top: 7px;
}

.landing-page .store-2 {
  align-items: center;
  display: flex;
  gap: 16px;
  left: 846px;
  position: absolute;
  top: 1977px;
  width: 312px;
}

.landing-page .content-4 {
  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
  left: 320px;
  position: absolute;
  top: 2449px;
  width: 641px;
}

.landing-page .list-types {
  align-items: center;
  border-radius: 8px;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
  position: relative;
}

.landing-page .notification-3 {
  align-items: center;
  background-color: #f8f8f7;
  border-radius: 42px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 55px;
  justify-content: center;
  overflow: hidden;
  padding: 10px;
  position: relative;
  width: 56px;
}

.landing-page .overlap-group-wrapper {
  height: 32px;
}

.landing-page .overlap-group-3 {
  height: 26px;
  left: 3px;
  position: relative;
  top: 3px;
  width: 27px;
}

.landing-page .path-9 {
  height: 2px;
  left: 10px;
  position: absolute;
  top: 7px;
  width: 11px;
}

.landing-page .path-10 {
  height: 2px;
  left: 10px;
  position: absolute;
  top: 12px;
  width: 10px;
}

.landing-page .path-11 {
  height: 2px;
  left: 10px;
  position: absolute;
  top: 17px;
  width: 3px;
}

.landing-page .path-12 {
  height: 26px;
  left: 0;
  position: absolute;
  top: 0;
  width: 26px;
}

.landing-page .path-13 {
  height: 10px;
  left: 15px;
  position: absolute;
  top: 16px;
  width: 12px;
}

.landing-page .path-14 {
  height: 3px;
  left: 5px;
  position: absolute;
  top: 12px;
  width: 3px;
}

.landing-page .path-15 {
  height: 3px;
  left: 5px;
  position: absolute;
  top: 7px;
  width: 3px;
}

.landing-page .path-16 {
  height: 3px;
  left: 5px;
  position: absolute;
  top: 16px;
  width: 3px;
}

.landing-page .seu-mercado-para {
  align-self: stretch;
  color: transparent;
  font-family: "Roboto-Thin", Helvetica;
  font-size: 36px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 39.6px;
  position: relative;
}

.landing-page .content-5 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
  left: 121px;
  position: absolute;
  top: 1972px;
  width: 470px;
}

.landing-page .text-wrapper-12 {
  align-self: stretch;
  color: #514d4f;
  font-family: "Roboto-ExtraLight", Helvetica;
  font-size: 36px;
  font-weight: 200;
  letter-spacing: 0;
  line-height: 39.6px;
  position: relative;
}

.landing-page .overlap-4 {
  height: 436px;
  left: 437px;
  position: absolute;
  top: 2708px;
  width: 766px;
}

.landing-page .chatgpt-image-de-2 {
  height: 400px;
  left: 93px;
  object-fit: cover;
  position: absolute;
  top: 36px;
  width: 600px;
}

.landing-page .card-status-pedido-4 {
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 4px 20px #514d4f1a;
  display: flex;
  flex-direction: column;
  gap: 32px;
  justify-content: center;
  left: 0;
  overflow: hidden;
  padding: 16px;
  position: absolute;
  top: 0;
  width: 219px;
}

.landing-page .card-status-pedido-5 {
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 4px 20px #514d4f1a;
  display: flex;
  flex-direction: column;
  gap: 32px;
  justify-content: center;
  left: 545px;
  overflow: hidden;
  padding: 16px;
  position: absolute;
  top: 76px;
  width: 221px;
}

.landing-page .card-status-pedido-6 {
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 4px 20px #514d4f1a;
  display: flex;
  flex-direction: column;
  gap: 32px;
  justify-content: center;
  left: 195px;
  overflow: hidden;
  padding: 16px;
  position: absolute;
  top: 293px;
  width: 358px;
}

.landing-page .button-4 {
  align-items: flex-start;
  align-self: stretch;
  border-radius: 8px;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 12px;
  justify-content: center;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.landing-page .product {
  background-color: #f8f8f7;
  border: 1px solid;
  border-color: #f2f2f1;
  border-radius: 8px;
  height: 32px;
  overflow: hidden;
  position: relative;
  width: 32px;
}

.landing-page .product-2 {
  height: 32px;
  left: 0;
  object-fit: cover;
  position: absolute;
  top: 0;
  width: 32px;
}

.landing-page .text-wrapper-13 {
  color: #514d4f;
  font-family: "SF Pro Text-Medium", Helvetica;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: normal;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .tag {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 6px;
  justify-content: center;
  position: relative;
}

.landing-page .img-5 {
  height: 16px;
  position: relative;
  width: 16px;
}

.landing-page .text-wrapper-14 {
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent;
  background: linear-gradient(
    180deg,
    rgb(183, 113, 229) 0%,
    rgb(236.79, 145.71, 145.71) 100%
  );
  background-clip: text;
  color: transparent;
  font-family: "SF Pro Text-Medium", Helvetica;
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 11px;
  position: relative;
  text-fill-color: transparent;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .content-categorias-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  position: relative;
}

.landing-page .product-wrapper {
  align-items: center;
  background-color: #f8f8f7;
  border-radius: 42px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 32px;
  justify-content: center;
  overflow: hidden;
  padding: 10px;
  position: relative;
  width: 32px;
}

.landing-page .product-3 {
  height: 24px;
  margin-bottom: -6.00px;
  margin-left: -6.00px;
  margin-right: -6.00px;
  margin-top: -6.00px;
  object-fit: cover;
  position: relative;
  width: 24px;
}

.landing-page .imagem-wrapper {
  align-items: center;
  background-color: #f8f8f7;
  border: 1px solid;
  border-color: #ffffff;
  border-radius: 42px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 38px;
  justify-content: center;
  margin-left: -6px;
  overflow: hidden;
  padding: 10px;
  position: relative;
  width: 38px;
}

.landing-page .imagem {
  height: 32px;
  margin-bottom: -7.00px;
  margin-left: -7.00px;
  margin-right: -7.00px;
  margin-top: -7.00px;
  object-fit: cover;
  position: relative;
  width: 32px;
}

.landing-page .notification-4 {
  align-items: center;
  background-color: #f8f8f7;
  border: 1px solid;
  border-color: #ffffff;
  border-radius: 42px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 32px;
  justify-content: center;
  margin-left: -6px;
  overflow: hidden;
  padding: 10px;
  position: relative;
  width: 32px;
}

.landing-page .text-wrapper-15 {
  color: #666666;
  font-family: "SF Pro Display-Regular", Helvetica;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-left: -1.00px;
  margin-right: -1.00px;
  margin-top: -2.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .text-wrapper-16 {
  align-self: stretch;
  color: #514d4f;
  font-family: "SF Pro Text-Regular", Helvetica;
  font-size: 10px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 11px;
  margin-top: -1.00px;
  position: relative;
}

.landing-page .text-wrapper-17 {
  color: #514d4f;
  font-family: "SF Pro Display-Bold", Helvetica;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 0;
  line-height: 17.6px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .img-6 {
  height: 14px;
  position: relative;
  width: 14px;
}

.landing-page .overlap-5 {
  height: 400px;
  left: 180px;
  position: absolute;
  top: 3292px;
  width: 874px;
}

.landing-page .chatgpt-image-de-3 {
  height: 400px;
  left: 195px;
  object-fit: cover;
  position: absolute;
  top: 0;
  width: 600px;
}

.landing-page .content-text {
  align-items: flex-start;
  background-color: #f8f8f7;
  border-radius: 16px;
  box-shadow: 0px 4px 20px #514d4f0d;
  display: flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
  left: 602px;
  padding: 16px;
  position: absolute;
  top: 223px;
  width: 272px;
}

.landing-page .text-wrapper-18 {
  align-self: stretch;
  color: #858082;
  font-family: "SF Pro Display-Bold", Helvetica;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
}

.landing-page .voc-vai-receber-um {
  align-self: stretch;
  color: #858082;
  font-family: "SF Pro Text-Regular", Helvetica;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  position: relative;
}

.landing-page .text-wrapper-19 {
  color: #858082;
  font-family: "SF Pro Text-Regular", Helvetica;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
}

.landing-page .card-status-pedido-7 {
  align-items: flex-start;
  background-color: #f8f8f7;
  border-radius: 8px;
  box-shadow: 0px 4px 20px #514d4f0d;
  display: flex;
  flex-direction: column;
  gap: 15px;
  justify-content: center;
  left: 0;
  overflow: hidden;
  padding: 16px;
  position: absolute;
  top: 35px;
  width: 350px;
}

.landing-page .card-documento {
  align-items: center;
  align-self: stretch;
  border-radius: 8px;
  display: flex;
  flex: 0 0 auto;
  gap: 16px;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.landing-page .img-wrapper {
  background-color: #f8f8f7;
  border: 1px solid;
  border-color: #f2f2f1;
  border-radius: 42px;
  height: 44px;
  overflow: hidden;
  position: relative;
  width: 44px;
}

.landing-page .product-4 {
  height: 44px;
  left: 0;
  object-fit: cover;
  position: absolute;
  top: 0;
  width: 44px;
}

.landing-page .content-6 {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 4px;
  position: relative;
}

.landing-page .text-wrapper-20 {
  color: #666666;
  font-family: "SF Pro Text-Regular", Helvetica;
  font-size: 11px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .steps {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 6px;
  position: relative;
  width: 100%;
}

.landing-page .frame {
  background-color: #85b29a;
  border-radius: 8px;
  flex: 1;
  flex-grow: 1;
  height: 4px;
  position: relative;
}

.landing-page .hora {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 6px;
  position: relative;
}

.landing-page .text-wrapper-21 {
  color: #666666;
  font-family: "SF Pro Text-Regular", Helvetica;
  font-size: 10px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 11px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .frame-2 {
  background-color: #cccccc;
  border-radius: 6px;
  height: 4px;
  position: relative;
  width: 4px;
}

.landing-page .text-wrapper-22 {
  color: #514d4f;
  font-family: "SF Pro Text-Regular", Helvetica;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 13.2px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .content-7 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 32px;
  left: 96px;
  position: absolute;
  top: 2822px;
  width: 318px;
}

.landing-page .text-wrapper-23 {
  align-self: stretch;
  color: #514d4f;
  font-family: "Roboto-Thin", Helvetica;
  font-size: 36px;
  font-weight: 100;
  letter-spacing: 0;
  line-height: 39.6px;
  margin-top: -1.00px;
  position: relative;
}

.landing-page .button-5 {
  all: unset;
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(183, 113, 229) 0%,
    rgb(236.79, 145.71, 145.71) 100%
  );
  border-radius: 52px;
  box-sizing: border-box;
  display: inline-flex;
  gap: 12px;
  height: 52px;
  justify-content: center;
  overflow: hidden;
  padding: 8px 32px;
  position: relative;
}

.landing-page .content-8 {
  align-items: center;
  display: inline-flex;
  gap: 32px;
  left: 121px;
  position: absolute;
  top: 2158px;
}

.landing-page .card-status-pedido-8 {
  align-items: flex-start;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 2px 10px #514d4f1a;
  display: flex;
  flex-direction: column;
  gap: 32px;
  justify-content: center;
  overflow: hidden;
  padding: 16px;
  position: relative;
  width: 182px;
}

.landing-page .notification-5 {
  height: 55px;
  position: relative;
  width: 56px;
}

.landing-page .text-wrapper-24 {
  color: #858082;
  font-family: "Helvetica Neue-Medium", Helvetica;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: normal;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .content-9 {
  align-items: flex-start;
  display: inline-flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
  left: 186px;
  position: absolute;
  top: 3903px;
}

.landing-page .text-wrapper-25 {
  color: #514d4f;
  font-family: "Roboto-ExtraLight", Helvetica;
  font-size: 36px;
  font-weight: 200;
  letter-spacing: 0;
  line-height: 39.6px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .button-6 {
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(183, 113, 229) 0%,
    rgb(236.79, 145.71, 145.71) 100%
  );
  border-radius: 52px;
  display: inline-flex;
  gap: 12px;
  height: 52px;
  justify-content: center;
  left: 186px;
  overflow: hidden;
  padding: 8px 32px;
  position: absolute;
  top: 4033px;
}

.landing-page .depoimento {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 16px;
  left: 640px;
  position: absolute;
  top: 3840px;
  width: 432px;
}

.landing-page .card-status-pedido-9 {
  align-items: flex-start;
  align-self: stretch;
  border: 1px solid;
  border-color: #ebebe9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  height: 261px;
  justify-content: space-between;
  overflow: hidden;
  padding: 16px;
  position: relative;
  width: 100%;
}

.landing-page .overlap-group-4 {
  height: 26px;
  left: 1px;
  position: relative;
  top: 3px;
  width: 30px;
}

.landing-page .rectangle {
  height: 6px;
  left: 19px;
  position: absolute;
  top: 1px;
  width: 7px;
}

.landing-page .path-17 {
  height: 8px;
  left: 17px;
  position: absolute;
  top: 5px;
  width: 5px;
}

.landing-page .path-18 {
  height: 8px;
  left: 23px;
  position: absolute;
  top: 5px;
  width: 5px;
}

.landing-page .path-19 {
  height: 2px;
  left: 0;
  position: absolute;
  top: 11px;
  width: 14px;
}

.landing-page .rectangle-2 {
  border: 2px solid;
  border-color: #514d4f;
  border-radius: 1.5px;
  height: 18px;
  left: 12px;
  position: absolute;
  top: 0;
  width: 6px;
}

.landing-page .path-20 {
  height: 15px;
  left: 2px;
  position: absolute;
  top: 11px;
  width: 26px;
}

.landing-page .path-21 {
  height: 10px;
  left: 2px;
  position: absolute;
  top: 3px;
  width: 12px;
}

.landing-page .path-22 {
  height: 2px;
  left: 16px;
  position: absolute;
  top: 11px;
  width: 14px;
}

.landing-page .content-10 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 32px;
  position: relative;
  width: 100%;
}

.landing-page .text-wrapper-26 {
  align-self: stretch;
  color: #666666;
  font-family: "Roboto-Light", Helvetica;
  font-size: 16px;
  font-weight: 300;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
}

.landing-page .content-11 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 2px;
  position: relative;
}

.landing-page .text-wrapper-27 {
  align-self: stretch;
  color: #514d4f;
  font-family: "Roboto-Regular", Helvetica;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
}

.landing-page .footer-2 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  padding: 0px 16px;
  position: relative;
  width: 100%;
}

.landing-page .steps-2 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 6px;
  position: relative;
}

.landing-page .frame-3 {
  background: linear-gradient(
    180deg,
    rgb(183, 113, 229) 0%,
    rgb(236.79, 145.71, 145.71) 100%
  );
  border-radius: 8px;
  height: 4px;
  position: relative;
  width: 16px;
}

.landing-page .frame-4 {
  background-color: #dbdbd6;
  border-radius: 8px;
  height: 4px;
  position: relative;
  width: 4px;
}

.landing-page .card-status-pedido-10 {
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(183, 113, 229) 0%,
    rgb(236.79, 145.71, 145.71) 100%
  );
  display: flex;
  justify-content: space-between;
  left: 0;
  padding: 24px 42px;
  position: fixed;
  top: 152px;
  width: 1280px;
}

.landing-page .use-o-cupom {
  color: #ffffff;
  font-family: "Roboto-Light", Helvetica;
  font-size: 24px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .text-wrapper-28 {
  font-weight: 300;
}

.landing-page .text-wrapper-29 {
  font-family: "Roboto-Bold", Helvetica;
  font-weight: 700;
}

.landing-page .text-wrapper-30 {
  color: #ffffff;
  font-family: "Roboto-Light", Helvetica;
  font-size: 16px;
  font-weight: 300;
  letter-spacing: 0;
  line-height: 17.6px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .header {
  align-items: center;
  background-color: #363435;
  border-radius: 10px 10px 0px 0px;
  box-shadow: inset 0px -0.5px 0px #000000;
  display: flex;
  gap: 74.5px;
  left: 0;
  overflow: hidden;
  padding: 12px 20px;
  position: fixed;
  top: 0;
  width: 1280px;
}

.landing-page .title-bar-buttons {
  height: 12px;
  position: relative;
  width: 52px;
}

.landing-page .minimize-button {
  background-color: #62c554;
  border: 0.6px solid;
  border-color: #0000001a;
  border-radius: 6px;
  height: 12px;
  left: 40px;
  position: absolute;
  top: 0;
  width: 12px;
}

.landing-page .zoom-button {
  background-color: #f4bf4e;
  border: 0.6px solid;
  border-color: #0000001a;
  border-radius: 6px;
  height: 12px;
  left: 20px;
  position: absolute;
  top: 0;
  width: 12px;
}

.landing-page .close-button {
  background-color: #ed6a5e;
  border: 0.6px solid;
  border-color: #0000001a;
  border-radius: 6px;
  height: 12px;
  left: 0;
  position: absolute;
  top: 0;
  width: 12px;
}

.landing-page .element {
  align-items: center;
  display: flex;
  gap: 10px;
  position: relative;
  width: 56px;
}

.landing-page .vertical-divider {
  height: 16px;
  position: relative;
  width: 1px;
}

.landing-page .small-arrow-down {
  height: 24px;
  position: relative;
  width: 12px;
}

.landing-page .details {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 15px;
  position: relative;
}

.landing-page .tabs {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 10px;
  position: relative;
}

.landing-page .active-tab {
  background-color: #ffffff24;
  border-radius: 6px;
  flex: 1;
  flex-grow: 1;
  height: 28px;
  overflow: hidden;
  position: relative;
}

.landing-page .domain {
  align-items: center;
  display: inline-flex;
  gap: 4px;
  left: 8px;
  position: absolute;
  top: 6px;
}

.landing-page .domain-2 {
  color: #ffffff;
  font-family: "SF Pro Display-Medium", Helvetica;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  width: fit-content;
}

.landing-page .lock {
  height: 13.54px;
  position: relative;
  width: 9.37px;
}

.landing-page .more-icon {
  height: 24px;
  left: 427px;
  position: absolute;
  top: 2px;
  width: 24px;
}

.landing-page .other-tabs {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 10px;
  position: relative;
}

.landing-page .dark-mode-collapsed {
  background-color: #ffffff14;
  border-radius: 6px;
  flex: 1;
  flex-grow: 1;
  height: 28px;
  overflow: hidden;
  position: relative;
}

.landing-page .website-favicon {
  height: 16px;
  left: 10px;
  position: absolute;
  top: 6px;
  width: 12px;
}

.landing-page .website-favicon-2 {
  height: 16px;
  left: 10px;
  object-fit: cover;
  position: absolute;
  top: 6px;
  width: 12px;
}

.landing-page .menu {
  -webkit-backdrop-filter: blur(16px) brightness(100%);
  align-items: center;
  backdrop-filter: blur(16px) brightness(100%);
  background-color: #ffffffe6;
  display: flex;
  justify-content: space-between;
  left: 0;
  padding: 24px 42px;
  position: fixed;
  top: 52px;
  width: 1280px;
  z-index: 1000;
}

.landing-page .logo {
  align-items: center;
  display: flex;
  gap: 4px;
  position: relative;
  width: 309px;
}

.landing-page .text-wrapper-31 {
  color: #858082;
  font-family: "Helvetica Neue-Medium", Helvetica;
  font-size: 32px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .menu-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 32px;
  position: relative;
}

.landing-page .text-wrapper-32 {
  color: #858082;
  font-family: "Roboto-Regular", Helvetica;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.landing-page .button-7 {
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(183, 113, 229) 0%,
    rgb(236.79, 145.71, 145.71) 100%
  );
  border-radius: 52px;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 12px;
  height: 52px;
  justify-content: center;
  overflow: hidden;
  padding: 8px 32px;
  position: relative;
}
